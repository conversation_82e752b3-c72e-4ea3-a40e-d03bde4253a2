using Poker.Api.Internal.Infrastructure;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Threading;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.ExceptionHandling;

namespace Poker.Api.Internal
{
    public class ProblemDetailsResult : IHttpActionResult
    {
        private readonly ExceptionContext _context;

        public ProblemDetailsResult(ExceptionContext context)
        {
            _context = context;
        }

        public Task<HttpResponseMessage> ExecuteAsync(CancellationToken cancellationToken)
        {
            var content = new ProblemDetails
            {
                Detail = _context.Exception.StackTrace,
                Instance = _context.Request.RequestUri.ToString(),
                Status = (int)HttpStatusCode.InternalServerError,
                Title = _context.Exception.Message,
                Type = _context.Exception.GetType().FullName
            };
            var response = new HttpResponseMessage(HttpStatusCode.InternalServerError)
            {
                Content = JsonContent.Create(content, new MediaTypeHeaderValue("application/problem+json")),
                RequestMessage = _context.Request
            };
            return Task.FromResult(response);
        }
    }
}