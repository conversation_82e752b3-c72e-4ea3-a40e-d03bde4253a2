using Microsoft.Extensions.Logging;
using Orleans.Runtime;
using Poker.Common.Data.Interface.Enums;
using Poker.Common.Data.Interface.Models.CashGames;
using Poker.Common.Data.Interface.Models.Transactions;
using Poker.Common.Data.Interface.Repositories;
using Poker.Engine.Interface;
using Poker.Engine.Interface.CashGames;
using Poker.Engine.Interface.Messaging;
using Poker.Engine.Interface.Rooms;
using Poker.Engine.Queue;
using Poker.Engine.Rooms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Poker.Engine.CashGames
{
    public class FastCashGameGrain : RoomGrain, IFastCashGameGrain
    {
        private readonly ICashGameRepository _cashGameRepository;
        private readonly CashGameTableProcessor _cashGameTableProcessor;
        private readonly CashGameRechargeService _cashGameRechargeService;
        private readonly LiveUpdateStream _liveUpdateStream;
        private readonly FastCashGameRankingService _fastCashGameRankingService;
        private CashGameDto _cashGame;

        public FastCashGameGrain(
            [PersistentState(nameof(state))] IPersistentState<RoomGrainState> state,
            ILogger<FastCashGameGrain> log,
            RoomPlayersProcessor roomPlayersProcessor,
            FastCashGameButtonRotator fastCashGameButtonRotator,
            IQueueService queueService,
            CashGameTableProcessor cashGameTableProcessor,
            ICashGameRepository cashGameRepository,
            CashGameRechargeService cashGameRechargeService,
            LiveUpdateStream liveUpdateStream,
            FastCashGameRankingService fastCashGameRankingService
        ) : base(state, log, roomPlayersProcessor, cashGameTableProcessor, fastCashGameButtonRotator, queueService)
        {
            _cashGameTableProcessor = cashGameTableProcessor;
            _cashGameRepository = cashGameRepository;
            _cashGameRechargeService = cashGameRechargeService;
            _liveUpdateStream = liveUpdateStream;
            _fastCashGameRankingService = fastCashGameRankingService;
        }

        protected override async Task<RoomType> OnInitializeAsync(int roomID, CancellationToken cancellationToken)
        {
            _cashGame = await _cashGameRepository.GetAsync(new CashGameDto(roomID), cancellationToken);
            if (_cashGame is null || !_cashGame.Active || _cashGame.RoomType is not RoomType.FastCashGame)
            {
                throw new InvalidOperationException($"Invalid fast cash game: RoomID={roomID}, Active={_cashGame?.Active}, RoomType={_cashGame?.RoomType}");
            }

            _cashGameTableProcessor.Initialize(this, _cashGame);
            return RoomType.FastCashGame;
        }

        public override async Task TablePlayerLeaveAsync(TablePlayerKey tablePlayerKey)
        {
            var tablePlayer = await Players.TableLeaveAsync(tablePlayerKey);
            if (tablePlayer is not null && (!State.TryGetSessionTablePlayers(tablePlayer.SessionRoom, out var tablePlayers) || tablePlayers.Count == 0))
            {
                if (await Players.RoomLeaveAsync(tablePlayer.SessionRoom))
                {
                    await SendPlayerCountAsync(tablePlayerKey.Player, false);
                }
            }
        }

        public override async Task TablePlayerChangeAsync(TablePlayerKey tablePlayerKey, decimal credit)
        {
            await Players.TableChangeAsync(tablePlayerKey, credit);
            await ReseatAsync();
        }

        public async Task<ClientStateCashGame> JoinAsync(PlayerKey playerKey, string nickname, SessionRoomKey? sessionRoomKey)
        {
            if (sessionRoomKey is null || !State.TryGetSession(sessionRoomKey.Value, out var player))
            {
                player = await Players.RoomJoinAsync(playerKey, nickname);
                Log.LogInformation("Join: SessionRoomID={SessionRoomID}, PlayerID={PlayerID}", player.SessionRoom, player.Player);
            }

            if (player.Table is not null)
            {
                var tableGrain = GrainFactory.GetTableGrain(player.Table.Value);
                var tableState = await tableGrain.GetClientStateAsync(player.Player);

                return new ClientStateCashGame
                {
                    Result = ClientStateCashGameResult.Success,
                    Player = new ClientStateCashGamePlayer
                    {
                        PlayerID = player.Player.ID,
                        SessionID = player.SessionRoom.ID
                    },
                    PlayerCount = State.SessionsCountJoined,
                    Table = tableState
                };
            }

            if (player.Credit == 0 && !player.HasJoinedTable)
            {
                return new ClientStateCashGame
                {
                    Result = ClientStateCashGameResult.Recharge,
                    Player = new ClientStateCashGamePlayer
                    {
                        PlayerID = player.Player.ID,
                        SessionID = player.SessionRoom.ID
                    },
                    PlayerCount = State.SessionsCountJoined
                };
            }

            return new ClientStateCashGame
            {
                Result = ClientStateCashGameResult.Failed
            };
        }

        public async Task<CashGameRechargeInfo> RechargeInfoAsync(SessionRoomKey sessionRoomKey)
        {
            if (!State.TryGetSession(sessionRoomKey, out var player))
            {
                Log.LogWarning("RechargeInfo player not sitting at table: SessionRoomID={SessionRoomID}", sessionRoomKey);
                return new CashGameRechargeInfo { Result = CashGameRechargeInfoResult.InvalidSession };
            }
            var tablePlayer = State.TryGetTablePlayer(sessionRoomKey, out var tablePlayerState) ? tablePlayerState : null;
            return await _cashGameRechargeService.RechargeInfoAsync(State, player, tablePlayer, _cashGame);
        }

        public async Task<CashGameRecharge> RechargeAsync(SessionRoomKey sessionRoomKey, decimal amount, bool auto)
        {
            if (!State.TryGetSession(sessionRoomKey, out var player))
            {
                Log.LogWarning("Recharge player not sitting at table: SessionRoomID={SessionRoomID}", sessionRoomKey);
                return new CashGameRecharge { Result = CashGameRechargeResult.InvalidSession };
            }

            var tablePlayer = State.TryGetTablePlayer(sessionRoomKey, out var tablePlayerState) ? tablePlayerState : null;
            var result = await _cashGameRechargeService.RechargeRoomAsync(State, player, tablePlayer, _cashGame, amount, auto);
            if (result.Result is CashGameRechargeResult.Success)
            {
                await _cashGameRechargeService.RechargeTableAsync(State, player, tablePlayer);
                await ReseatAsync();
            }
            return result;
        }

        private async Task ReseatAsync()
        {
            await ReseatUpdateModeAsync();

            var tablesChanged = new SortedSet<TableKey>();

            foreach (var player in State.SessionsActive)
            {
                if (player.Credit > 0 && player.Table is null)
                {
                    var (tableID, place) = await ReaseatFindTableAsync(player);
                    await ReseatJoinTableAsync(player, tableID, place);
                    tablesChanged.Add(tableID);
                }
            }

            var tables = State.Tables.Where(x => x.TableChangeEnabled && x.HandID is not null).ToArray();
            foreach (var table in tables)
            {
                if (State.TryGetTable(table.Table, out _, out var tablePlayers))
                {
                    foreach (var tablePlayer in tablePlayers)
                    {
                        if (!tablePlayer.Hand && !tablePlayer.Abandoned)
                        {
                            var player = await Players.TableChangeAsync(tablePlayer.TablePlayer, null);
                            var (tableID, place) = await ReaseatFindTableAsync(player);
                            await ReseatJoinTableAsync(player, tableID, place);
                            tablesChanged.Add(tableID);
                        }
                    }
                }
            }

            foreach (var tableID in tablesChanged)
            {
                await _cashGameTableProcessor.HandTryStartAsync(tableID);
            }
        }

        private async Task ReseatUpdateModeAsync()
        {
            var tableChangeEnabled = State.SessionsCountJoined > _cashGame.PlayersMax;
            if (tableChangeEnabled && !State.Room.TableChangeEnabled)
            {
                var tables = State.Tables.Where(x => !x.TableChangeEnabled).ToArray();

                await State.UpdateAsync(x =>
                {
                    x.UpdateRoom(y => y.TableChangeEnabled = true);
                    foreach (var table in tables)
                    {
                        x.UpdateTable(table, y => y.TableChangeEnabled = true);
                    }
                });

                foreach (var table in tables)
                {
                    var tableGrain = GrainFactory.GetTableGrain(table.Table);
                    await tableGrain.ChangeEnableAsync();
                }
            }
        }

        private async Task<(TableKey Table, byte Place)> ReaseatFindTableAsync(IRoomPlayerState player)
        {
            var playersMax = State.Room.TableChangeEnabled ? State.PlayersCountJoined switch
            {
                < 9 => 4,
                < 11 => 5,
                >= 11 => 6
            } : _cashGame.PlayersMax;

            foreach (var table in State.Tables)
            {
                if (ReaseatFindPlace(table.Table, player, playersMax, out var place))
                {
                    return (table.Table, place);
                }
            }

            var newTable = _cashGameTableProcessor.GetTable();
            var tableID = await Players.CreateTableAsync(newTable);

            if (ReaseatFindPlace(tableID, player, playersMax, out var newPlace))
            {
                return (tableID, newPlace);
            }
            throw new InvalidOperationException($"ReaseatFindTable failed to find place: TableID={tableID}, PlayerID={player.Player}");
        }

        private bool ReaseatFindPlace(TableKey tableKey, IRoomPlayerState player, int playersMax, out byte place)
        {
            if (State.TryGetTable(tableKey, out var table, out var tablePlayers))
            {
                if (tablePlayers.Count < playersMax && !tablePlayers.Any(x => x.Player == player.Player) && (!table.TableChangeEnabled || table.HandID is null))
                {
                    var placesTaken = tablePlayers.Where(x => x.Place is not null).Select(x => x.Place.Value).ToArray();
                    var places = Enumerable.Range(1, _cashGame.PlayersMax)
                        .Select(x => (byte)x)
                        .Except(placesTaken)
                        .OrderBy(x => x)
                        .ToArray();

                    var rank = _fastCashGameRankingService.GetRank(player.Player);
                    place = rank switch
                    {
                        > 0 => places.First(),
                        < 0 => places.Last(),
                        _ => places.MinBy(x => Math.Abs(x - (_cashGame.PlayersMax + 1) / 2m))
                    };
                    return true;
                }
            }
            place = 0;
            return false;
        }

        private async Task ReseatJoinTableAsync(IRoomPlayerState player, TableKey table, byte place)
        {
            var newPlayer = !player.HasJoinedTable;
            var tablePlayer = await Players.TableJoinAsync(player, table, place);
            await _cashGameRechargeService.RechargeTableAsync(State, player, tablePlayer);

            if (newPlayer)
            {
                await SendPlayerCountAsync(player.Player, true);
            }
        }

        private async Task SendPlayerCountAsync(PlayerKey player, bool hasSession)
        {
            var playerCount = State.SessionsCountJoined;
            foreach (var table in State.Tables)
            {
                var tableGrain = GrainFactory.GetTableGrain(table.Table);
                await tableGrain.TransactionAsync(new TableTransactionDto
                {
                    TypeSub = TableTransactionType.PlayerCount,
                    TableID = table.Table.ID,
                    PlayerCount = playerCount
                });
            }
            await _liveUpdateStream.PublishAsync(new EventCashGamePlayersChanged
            {
                RoomType = RoomType.FastCashGame,
                GameID = _cashGame.ID,
                PlayerID = player.ID,
                HasSession = hasSession,
                PlayersCount = playerCount
            });
        }
    }
}