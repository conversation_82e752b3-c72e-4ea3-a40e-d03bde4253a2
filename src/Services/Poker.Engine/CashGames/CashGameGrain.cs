using Microsoft.Extensions.Logging;
using Orleans.Runtime;
using Poker.Common.Data.Interface.Enums;
using Poker.Common.Data.Interface.Models.CashGames;
using Poker.Common.Data.Interface.Repositories;
using Poker.Common.Infrastructure.Settings;
using Poker.Engine.Interface;
using Poker.Engine.Interface.CashGames;
using Poker.Engine.Interface.Messaging;
using Poker.Engine.Interface.Rooms;
using Poker.Engine.Queue;
using Poker.Engine.Rooms;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Poker.Engine.CashGames
{
    public class CashGameGrain : RoomGrain, ICashGameGrain
    {
        private readonly CashGameTableProcessor _cashGameTableProcessor;
        private readonly IQueueService _queueService;
        private readonly ICashGameRepository _cashGameRepository;
        private readonly CashGameRechargeService _cashGameRechargeService;
        private readonly LiveUpdateStream _liveUpdateStream;
        private readonly ISettings _settings;
        private readonly TimeProvider _timeProvider;
        private CashGameDto _cashGame;

        public CashGameGrain(
            [PersistentState(nameof(state))] IPersistentState<RoomGrainState> state,
            ILogger<CashGameGrain> log,
            RoomPlayersProcessor roomPlayersProcessor,
            CashGameTableProcessor cashGameTableProcessor,
            CashGameButtonRotator cashGameButtonRotator,
            IQueueService queueService,
            ICashGameRepository cashGameRepository,
            CashGameRechargeService cashGameRechargeService,
            LiveUpdateStream liveUpdateStream,
            ISettings settings,
            TimeProvider timeProvider
        ) : base(state, log, roomPlayersProcessor, cashGameTableProcessor, cashGameButtonRotator, queueService)
        {
            _cashGameTableProcessor = cashGameTableProcessor;
            _queueService = queueService;
            _cashGameRepository = cashGameRepository;
            _cashGameRechargeService = cashGameRechargeService;
            _liveUpdateStream = liveUpdateStream;
            _settings = settings;
            _timeProvider = timeProvider;
        }

        protected override async Task<RoomType> OnInitializeAsync(int roomID, CancellationToken cancellationToken)
        {
            _cashGame = await _cashGameRepository.GetAsync(new CashGameDto(roomID), cancellationToken);
            if (_cashGame is null || !_cashGame.Active || _cashGame.RoomType is not RoomType.CashGame)
            {
                throw new InvalidOperationException($"Invalid cash game: RoomID={roomID}, Active={_cashGame?.Active}, RoomType={_cashGame?.RoomType}");
            }

            _cashGameTableProcessor.Initialize(this, _cashGame);
            return RoomType.CashGame;
        }

        public override async Task<bool> TablePlayerSitAsync(IRoomTablePlayerState tablePlayer, bool sitIn, byte? place = null)
        {
            if (tablePlayer.Place is null)
            {
                if (place is null or < 1 || place > _cashGame.PlayersMax)
                {
                    Log.LogInformation("TablePlayerSit invalid place: Place={Place}", place);
                    return false;
                }

                if (State.PlaceTaken(place.Value))
                {
                    Log.LogInformation("TablePlayerSit place taken");
                    return false;
                }
            }

            await Players.TableSitAsync(tablePlayer.TablePlayer, sitIn, place);
            await _liveUpdateStream.PublishAsync(new EventCashGamePlayersChanged
            {
                RoomType = RoomType.CashGame,
                GameID = _cashGame.ID,
                PlayerID = tablePlayer.Player.ID,
                HasSession = true,
                PlayersCount = State.SessionsCountSeated
            });

            if (!sitIn && State.TryGetTablePlayer(tablePlayer.TablePlayer, out tablePlayer, out var player))
            {
                var timeout = (tablePlayer.HandLast ?? tablePlayer.SitInFirst ?? player.Joined) + _settings.Timeout.LeaveTimeoutIdle;
                var queueKey = _queueService.Queue(timeout, new QueueActionCashGameLeaveIdle(RoomKey, player.SessionRoom));
                await State.UpdateAsync(x => x.UpdateSession(player, y =>
                {
                    y.IdleQueueKey = queueKey;
                }));
            }
            return true;
        }

        public override async Task TablePlayerLeaveAsync(TablePlayerKey tablePlayerKey)
        {
            var tablePlayer = await Players.TableLeaveAsync(tablePlayerKey);
            if (tablePlayer is not null && (!State.TryGetSessionTablePlayers(tablePlayer.SessionRoom, out var tablePlayers) || tablePlayers.Count == 0))
            {
                if (await Players.RoomLeaveAsync(tablePlayer.SessionRoom))
                {
                    await _liveUpdateStream.PublishAsync(new EventCashGamePlayersChanged
                    {
                        RoomType = RoomType.CashGame,
                        GameID = _cashGame.ID,
                        PlayerID = tablePlayerKey.Player.ID,
                        HasSession = true,
                        PlayersCount = State.SessionsCountSeated
                    });
                }
            }
        }

        public async Task<ClientStateCashGame> JoinAsync(PlayerKey playerKey, string nickname)
        {
            if (!State.TryGetPlayer(playerKey, out var player))
            {
                if (State.Tables.Count == 0)
                {
                    var table = _cashGameTableProcessor.GetTable();
                    await Players.CreateTableAsync(table);
                }
                var tableID = State.Tables.Single().Table;

                player = await Players.RoomJoinAsync(playerKey, nickname);
                await Players.TableJoinAsync(player, tableID, null);

                await State.UpdateAsync(x => x.UpdateSession(player, y =>
                {
                    y.ObserverQueueKey = _queueService.Queue(player.Joined + _settings.Timeout.LeaveTimeoutObserver, new QueueActionCashGameLeaveObserver(RoomKey, player.SessionRoom));
                }));
            }

            if (player.Table is not null)
            {
                var tableGrain = GrainFactory.GetTableGrain(player.Table.Value);
                var tableState = await tableGrain.GetClientStateAsync(player.Player);

                return new ClientStateCashGame
                {
                    Result = ClientStateCashGameResult.Success,
                    Player = new ClientStateCashGamePlayer
                    {
                        PlayerID = player.Player.ID,
                        SessionID = player.SessionRoom.ID
                    },
                    PlayerCount = State.SessionsCountSeated,
                    Table = tableState
                };
            }

            return new ClientStateCashGame
            {
                Result = ClientStateCashGameResult.Failed
            };
        }

        public async Task<CashGameRechargeInfo> RechargeInfoAsync(SessionRoomKey sessionRoomKey)
        {
            if (!State.TryGetSession(sessionRoomKey, out var player) || !State.TryGetTablePlayer(sessionRoomKey, out var tablePlayer) || tablePlayer?.Place is null)
            {
                Log.LogWarning("RechargeInfo player not sitting at table: SessionRoomID={SessionRoomID}, PlayerID={PlayerID}, TableID={TableID}", sessionRoomKey, player?.Player, player?.Table);
                return new CashGameRechargeInfo { Result = CashGameRechargeInfoResult.InvalidSession };
            }
            return await _cashGameRechargeService.RechargeInfoAsync(State, player, tablePlayer, _cashGame);
        }

        public async Task<CashGameRecharge> RechargeAsync(SessionRoomKey sessionRoomKey, decimal amount, bool auto)
        {
            if (!State.TryGetSession(sessionRoomKey, out var player) || !State.TryGetTablePlayer(sessionRoomKey, out var tablePlayer) || tablePlayer?.Place is null)
            {
                Log.LogWarning("Recharge player not sitting at table: SessionRoomID={SessionRoomID}, PlayerID={PlayerID}, TableID={TableID}", sessionRoomKey, player?.Player, player?.Table);
                return new CashGameRecharge { Result = CashGameRechargeResult.InvalidSession };
            }

            var result = await _cashGameRechargeService.RechargeRoomAsync(State, player, tablePlayer, _cashGame, amount, auto);
            if (result.Result is CashGameRechargeResult.Success)
            {
                await _cashGameRechargeService.RechargeTableAsync(State, player, tablePlayer);
                await _cashGameTableProcessor.HandTryStartAsync(tablePlayer.Table);
            }
            return result;
        }

        public async Task TimeoutObserverAsync(SessionRoomKey playerKey, Guid queueKey)
        {
            if (!State.TryGetSession(playerKey, out var player) || player.ObserverQueueKey != queueKey)
            {
                return;
            }

            if (!State.TryGetTablePlayer(playerKey, out var tablePlayer) || tablePlayer.Place is not null)
            {
                return;
            }

            await TablePlayerLeaveAsync(tablePlayer.TablePlayer);
        }

        public async Task TimeoutIdleAsync(SessionRoomKey playerKey, Guid queueKey)
        {
            if (!State.TryGetSession(playerKey, out var player) || player.IdleQueueKey != queueKey)
            {
                return;
            }

            if (!State.TryGetTablePlayer(playerKey, out var tablePlayer))
            {
                return;
            }

            var timeout = (tablePlayer.HandLast ?? tablePlayer.SitInFirst ?? player.Joined) + _settings.Timeout.LeaveTimeoutIdle;
            if (timeout > _timeProvider.GetUtcNow())
            {
                return;
            }

            await TablePlayerLeaveAsync(tablePlayer.TablePlayer);
        }

        public async Task TimeoutRechargeAsync(SessionRoomKey playerKey, Guid queueKey)
        {
            if (!State.TryGetSession(playerKey, out var player) || player.RechargeQueueKey != queueKey)
            {
                return;
            }

            if (!State.TryGetTablePlayer(playerKey, out var tablePlayer) || tablePlayer.Credit >= _cashGame.BigBlind)
            {
                return;
            }

            var timeout = (tablePlayer.HandLast ?? tablePlayer.SitInFirst ?? player.Joined) + _settings.Timeout.LeaveTimeoutRecharge;
            if (timeout > _timeProvider.GetUtcNow())
            {
                return;
            }

            await TablePlayerLeaveAsync(tablePlayer.TablePlayer);
        }
    }
}