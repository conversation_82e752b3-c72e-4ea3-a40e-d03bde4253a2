using Orleans;
using Poker.Common.Data.Interface.Models.Transactions;
using Poker.Common.Data.Interface.Repositories;
using Poker.Common.Logic.Messaging;
using Poker.Engine.Interface.Messaging;
using System.Threading.Tasks;

namespace Poker.Engine.Tables
{
    public class TableTransactionProcessor
    {
        private readonly ITransactionsGrain _transactionsGrain;
        private readonly ITransactionRepository _transactionRepository;
        private long _lastID = -1;
        private int _lastSequence;

        public TableTransactionProcessor(IGrainFactory grainFactory, ITransactionRepository transactionRepository)
        {
            _transactionRepository = transactionRepository;
            _transactionsGrain = grainFactory.GetGrain<ITransactionsGrain>(0);
        }

        public async Task<long> PublishAsync(TableGrain grain, TransactionDto transaction)
        {
            transaction.TableID = grain.TableKey.ID;

            await GetLastAsync(grain);
            TransactionMessageSerializer.Serialize(transaction);
            transaction.Sequence = ++_lastSequence;
            await _transactionRepository.CreateAsync(transaction);

            await _transactionsGrain.PublishAsync(new TransactionMessageDto
            {
                ID = transaction.ID,
                TableID = transaction.TableID,
                Type = transaction.Type,
                Created = transaction.Created,
                Sequence = transaction.Sequence.Value,
                MessageText = transaction.MessageText,
                MessagePlayerID = transaction.MessagePlayerID,
                MessagePlayerText = transaction.MessagePlayerText
            });

            return _lastID = transaction.ID;
        }

        public async Task<TransactionDto> GetLastAsync(TableGrain grain)
        {
            if (_lastID < 0)
            {
                var last = await _transactionRepository.GetLastAsync(grain.TableKey.ID);
                if (last.ID > _lastID)
                {
                    _lastID = last.ID;
                }
                if (last.Sequence > _lastSequence)
                {
                    _lastSequence = last.Sequence.Value;
                }
            }
            return new TransactionDto { ID = _lastID, Sequence = _lastSequence };
        }
    }
}