using Poker.Common.Data.Interface.Enums;
using Poker.Common.Data.Interface.Models.Hands;
using Poker.Common.Data.Interface.Models.Transactions;
using Poker.Common.Data.Interface.Repositories;
using Poker.Engine.Interface.Tables;
using System;
using System.Collections.Immutable;
using System.Linq;
using System.Threading.Tasks;

namespace Poker.Engine.Tables
{
    public class TableHandProcessor
    {
        private readonly TimeProvider _timeProvider;
        private readonly IHandRepository _handRepository;

        public TableHandProcessor(
            TimeProvider timeProvider,
            IHandRepository handRepository
        )
        {
            _timeProvider = timeProvider;
            _handRepository = handRepository;
        }

        public async Task<int> HandStartAsync(TableGrain table, ImmutableArray<TableHandStartPlayer> players)
        {
            var hand = await _handRepository.CreateAsync(new HandDto
            {
                TableID = table.TableKey.ID,
                PlayersCount = (byte)players.Length
            });
            var transactionID = await table.TransactionAsync(new HandTransactionDto
            {
                TableID = table.TableKey.ID,
                TypeSub = HandTransactionType.Initialize,
                HandID = hand.ID
            });
            await _handRepository.UpdateInitializedAsync(hand.ID, transactionID);

            foreach (var player in table.State.Players.Where(x => x.Place is not null).OrderBy(x => x.Player.ID))
            {
                var handPlayer = players.FirstOrDefault(x => x.Player == player.Player);
                await table.Players.HandStartAsync(player, hand.ID, handPlayer?.Order, handPlayer?.Role);
            }

            await table.State.UpdateAsync(x =>
            {
                x.Phase = TablePhase.Blinds_Initialize;
                x.SetHand(new TableHandState { HandID = hand.ID });
            });

            await table.ProcessPhaseAsync();
            return hand.ID;
        }

        public async Task HandFinishAsync(TableGrain table, ImmutableArray<TableHandFinishPlayer> players)
        {
            var commission = table.State.HandPlayers.Sum(x => x.HandCommission);
            var transactionID = await table.TransactionAsync(new HandTransactionDto
            {
                TableID = table.TableKey.ID,
                TypeSub = HandTransactionType.Terminate,
                HandID = table.State.HandID.Value,
                Commission = commission > 0 ? commission : null
            });
            await _handRepository.UpdateTerminatedAsync(table.State.HandID.Value, transactionID);

            foreach (var player in table.State.HandPlayers)
            {
                var handPlayer = players.FirstOrDefault(x => x.Player == player.Player);
                await table.Players.HandFinishAsync(player, handPlayer?.Credit ?? player.TableCredit);
            }

            await table.State.UpdateAsync(x =>
            {
                x.Phase = TablePhase.Waiting;
                x.SetHand(null);
            });
        }

        public async Task<TimeSpan> HandMinPlayersReachedAsync(TableGrain table)
        {
            if (table.State.Common.MinPlayersWait)
            {
                await table.State.UpdateAsync(x =>
                {
                    x.UpdateCommon(y => y.MinPlayersWait = false);
                });
                await table.TransactionAsync(new TableTransactionDto
                {
                    TypeSub = TableTransactionType.MinPlayersReached,
                    TableID = table.TableKey.ID
                });
            }
            if (table.State.Common.MinPlayersReached is null)
            {
                await table.State.UpdateAsync(x =>
                {
                    x.UpdateCommon(y => y.MinPlayersReached = _timeProvider.GetUtcNow().AddSeconds(3));
                });
            }
            return table.State.Common.MinPlayersReached.Value - _timeProvider.GetUtcNow();
        }

        public async Task HandMinPlayersWaitAsync(TableGrain table)
        {
            if (!table.State.Common.MinPlayersWait)
            {
                await table.State.UpdateAsync(x =>
                {
                    x.UpdateCommon(y => y.MinPlayersWait = true);
                });
                await table.TransactionAsync(new TableTransactionDto
                {
                    TypeSub = TableTransactionType.MinPlayersWait,
                    TableID = table.TableKey.ID
                });
            }
        }
    }
}