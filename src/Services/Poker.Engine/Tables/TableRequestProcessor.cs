using Microsoft.Extensions.Logging;
using Poker.Common.Data.Interface.Enums;
using Poker.Common.Data.Interface.Models.Transactions;
using Poker.Common.Logic.Interface.Services;
using Poker.Engine.Interface;
using Poker.Engine.Interface.Tables;
using Poker.Engine.Queue;
using System.Linq;
using System.Threading.Tasks;

namespace Poker.Engine.Tables
{
    public class TableRequestProcessor
    {
        private readonly ILogger<TableRequestProcessor> _log;
        private readonly IChatService _chatService;
        private readonly IQueueService _queueService;

        public TableRequestProcessor(
            ILogger<TableRequestProcessor> log,
            IChatService chatService,
            IQueueService queueService
        )
        {
            _log = log;
            _chatService = chatService;
            _queueService = queueService;
        }

        public async Task<bool> MessageAsync(TableGrain table, PlayerKey playerKey, string message)
        {
            if (!table.State.Common.IsChatEnabled)
            {
                return false;
            }

            if (!table.State.TryGetPlayer(playerKey, out var player) || player.Place is null)
            {
                _log.LogInformation("Message player not at table");
                return false;
            }

            if (!player.IsChatEnabled)
            {
                _log.LogInformation("Message player chat disabled");
                return false;
            }

            message = _chatService.Censor(message);
            if (string.IsNullOrEmpty(message))
            {
                return false;
            }

            await table.TransactionAsync(new SitTransactionDto
            {
                TableID = player.Table.ID,
                TypeSub = SitTransactionType.Message,
                PlayerID = player.Player.ID,
                Nickname = player.Nickname,
                SessionGuid = player.SessionGuid,
                Place = player.Place,
                Message = message
            });

            return true;
        }

        public async Task<bool> ActionAsync(TableGrain table, PlayerKey playerKey, StepTransactionAction action, decimal? amount)
        {
            return action switch
            {
                StepTransactionAction.SmallBlindPaid => await ActionAsync(table, playerKey, [TablePhase.Blinds_MandatoryBets], action, StepTransactionChoice.PaySB, amount),
                StepTransactionAction.BigBlindPaid => await ActionAsync(table, playerKey, [TablePhase.Blinds_MandatoryBets], action, StepTransactionChoice.PayBB, amount),
                StepTransactionAction.Fold => await ActionAsync(table, playerKey, [TablePhase.Blinds_Bets, TablePhase.Flop_Bets, TablePhase.Turn_Bets, TablePhase.River_Bets], action, StepTransactionChoice.Fold, amount),
                StepTransactionAction.Check => await ActionAsync(table, playerKey, [TablePhase.Blinds_Bets, TablePhase.Flop_Bets, TablePhase.Turn_Bets, TablePhase.River_Bets], action, StepTransactionChoice.Check, amount),
                StepTransactionAction.Raise => await ActionAsync(table, playerKey, [TablePhase.Blinds_Bets, TablePhase.Flop_Bets, TablePhase.Turn_Bets, TablePhase.River_Bets], action, StepTransactionChoice.Raise, amount),
                StepTransactionAction.Bet => await ActionAsync(table, playerKey, [TablePhase.Blinds_Bets, TablePhase.Flop_Bets, TablePhase.Turn_Bets, TablePhase.River_Bets], action, StepTransactionChoice.Bet, amount),
                StepTransactionAction.Call => await ActionAsync(table, playerKey, [TablePhase.Blinds_Bets, TablePhase.Flop_Bets, TablePhase.Turn_Bets, TablePhase.River_Bets], action, StepTransactionChoice.Call, amount),
                StepTransactionAction.AllIn => await ActionAsync(table, playerKey, [TablePhase.Blinds_Bets, TablePhase.Flop_Bets, TablePhase.Turn_Bets, TablePhase.River_Bets], action, StepTransactionChoice.AllIn, amount),
                StepTransactionAction.Show => await ActionAsync(table, playerKey, [TablePhase.Showdown_ShowCards], action, StepTransactionChoice.Show, amount),
                StepTransactionAction.DontShow => await ActionAsync(table, playerKey, [TablePhase.Showdown_ShowCards], action, StepTransactionChoice.DontShow, amount),
                StepTransactionAction.Muck => await ActionAsync(table, playerKey, [TablePhase.Showdown_ShowCards], action, StepTransactionChoice.Muck, amount),
                StepTransactionAction.PenaltyPaid => await ActionAsync(table, playerKey, [TablePhase.Blinds_MandatoryBets], action, StepTransactionChoice.PayPenalty, amount),
                StepTransactionAction.Wait => await ActionAsync(table, playerKey, [TablePhase.Blinds_MandatoryBets], action, StepTransactionChoice.Wait, amount),
                StepTransactionAction.FoldQuick => await FoldQuickAsync(table, playerKey),
                _ => false
            };
        }

        private async Task<bool> ActionAsync(TableGrain table, PlayerKey playerKey, TablePhase[] phases, StepTransactionAction action, StepTransactionChoice choice, decimal? amount)
        {
            if (!phases.Contains(table.State.Phase))
            {
                _log.LogInformation("Action invalid phase: Action={Action}, Phase={Phase},", action, table.State.Phase);
                return false;
            }

            var player = table.State.RoundPlayers.FirstOrDefault(x => x.Player == playerKey);
            if (player is null)
            {
                _log.LogInformation("Action not in round: Action={Action}, PlayerID={PlayerID}", action, action);
                return false;
            }

            if (player.Turn?.Choice.HasFlag(choice) is not true)
            {
                _log.LogInformation("Action invalid choice: Action={Action}", action);
                return false;
            }

            await table.Players.PlayAsync(player, action, amount);
            await table.ProcessPhaseAsync();

            return true;
        }

        private async Task<bool> FoldQuickAsync(TableGrain table, PlayerKey playerKey)
        {
            if (table.State.Phase is < TablePhase.Blinds_Bets or > TablePhase.River_Bets)
            {
                _log.LogInformation("FoldQuick invalid phase: Phase={Phase},", table.State.Phase);
                return false;
            }

            var player = table.State.RoundPlayers.FirstOrDefault(x => x.Player == playerKey);
            if (player is null)
            {
                _log.LogInformation("FoldQuick not in round: PlayerID={PlayerID}", playerKey);
                return false;
            }

            if (player.ChangedTable)
            {
                _log.LogInformation("FoldQuick already changed table: PlayerID={PlayerID}", playerKey);
                return false;
            }

            await table.State.UpdateAsync(x =>
            {
                x.UpdatePlayer(player, y =>
                {
                    y.ChangedTable = true;
                });
            });

            _queueService.Queue(new QueueActionRoomPlayerChange(table.RoomKey, player.TablePlayer, player.CurrentCredit));
            return true;
        }
    }
}