using Microsoft.Extensions.Logging;
using Orleans;
using Orleans.Placement;
using Orleans.Runtime;
using Poker.Common.Data.Interface.Enums;
using Poker.Common.Data.Interface.Models.Sessions;
using Poker.Common.Data.Interface.Models.Tables;
using Poker.Common.Data.Interface.Models.Transactions;
using Poker.Common.Data.Interface.Repositories;
using Poker.Engine.Interface;
using Poker.Engine.Interface.Tables;
using Poker.Engine.Queue;
using Poker.Engine.Tables.Phases;
using Poker.Engine.Tables.Players;
using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Poker.Engine.Tables
{
    [PreferLocalPlacement]
    public class TableGrain : Grain, ITableGrain, IIncomingGrainCallFilter
    {
        private readonly ILogger<TableGrain> _log;
        private readonly ITableRepository _tableRepository;
        private readonly TablePhaseProcessor _tablePhaseProcessor;
        private readonly TableRequestProcessor _tableRequestProcessor;
        private readonly TableTransactionProcessor _tableTransactionProcessor;
        private readonly TableHandProcessor _tableHandProcessor;
        private readonly IQueueService _queueService;

        private Dictionary<string, object> _logState = new();

        public TableGrain(
            [PersistentState(nameof(state))] IPersistentState<TableGrainState> state,
            ILogger<TableGrain> log,
            ITableRepository tableRepository,
            TableHandProcessor tableHandProcessor,
            TablePhaseProcessor tablePhaseProcessor,
            TablePlayersProcessor tablePlayersProcessor,
            TableRequestProcessor tableRequestProcessor,
            TableTransactionProcessor tableTransactionProcessor,
            IQueueService queueService
        )
        {
            State = new TableGrainStateWrapper(state, log, queueService);
            _log = log;
            _tableRepository = tableRepository;
            _tableHandProcessor = tableHandProcessor;
            _tablePhaseProcessor = tablePhaseProcessor;
            _tableRequestProcessor = tableRequestProcessor;
            _tableTransactionProcessor = tableTransactionProcessor;
            _queueService = queueService;
            Players = tablePlayersProcessor;
            tablePhaseProcessor.Initialize(this);
            tablePlayersProcessor.Initialize(this);
        }

        public ITableGrainStateWrapper State { get; }
        public RoomKey RoomKey { get; private set; }
        public TableKey TableKey { get; private set; }
        public TableGrainSettings Settings { get; private set; }
        public TablePlayersProcessor Players { get; }

        public async Task Invoke(IIncomingGrainCallContext context)
        {
            using (_log.BeginScope(_logState))
            {
                await context.Invoke();
            }
        }

        public override async Task OnActivateAsync(CancellationToken cancellationToken)
        {
            var tableID = (int)this.GetPrimaryKeyLong();

            var table = await _tableRepository.GetAsync(new TableDto(tableID), cancellationToken, x => x.Room);
            if (table is null)
            {
                throw new InvalidOperationException($"Invalid table: TableID={tableID}");
            }

            RoomKey = new(table.Room.Type, table.RoomID);
            TableKey = new(table.ID);
            Settings = new TableGrainSettings
            {
                Ante = table.Ante,
                SmallBlind = table.SmallBlind,
                BigBlind = table.BigBlind,
                LimitLow = table.LimitLow,
                LimitHigh = table.LimitHigh,
                TablePlayersMin = table.PlayersMin,
                TablePlayersMax = table.PlayersMax,
                TurnTimeout = table.TurnTimeout,
                RulesType = table.RulesType
            };

            _logState = new Dictionary<string, object>
            {
                [nameof(SessionTableInfoDto.RoomType)] = RoomKey.Type,
                [nameof(SessionTableInfoDto.RoomID)] = RoomKey.ID,
                [nameof(SessionTableInfoDto.TableID)] = table.ID
            };
        }

        public Task PingAsync()
        {
            foreach (var player in State.Players)
            {
                if (player.Turn is not null)
                {
                    _queueService.Queue(player.Turn.End, new QueueActionTableTurnTimeout(player.TablePlayer), player.Turn.QueueKey);
                }
            }
            return Task.CompletedTask;
        }

        public Task<TableGrainState> GetStateAsync()
        {
            return Task.FromResult(((TableGrainStateWrapper)State).State);
        }

        public async Task<ClientStateTable> GetClientStateAsync(PlayerKey playerKey)
        {
            if (!State.TryGetPlayer(playerKey, out var session))
            {
                return null;
            }

            var lastTransaction = await _tableTransactionProcessor.GetLastAsync(this);

            var players = State.Players.Where(x => x.Place is not null).OrderBy(x => x.Player.ID).Select(x => new ClientStateTablePlayer
            {
                PlayerID = x.Player.ID,
                Nickname = x.Nickname,

                SitType = x.SitIn ? PlayerSitType.SitIn : PlayerSitType.SitOut,
                Place = x.Place,
                Role = x.HandRole,
                LastAction = x.HandAction,
                IsChatEnabled = x.IsChatEnabled,
                Choice = null,

                TableCredit = 0,
                LiveBet = 0,
                ChoiceCallAmount = null,
                ChoiceRaiseAmount = null,
                ChoiceTransactionID = null,

                TurnTimeElapsed = 0,
                TurnTime = 0,
                TimeBank = 0,
                TimeBankDisconnection = 0,

                Cards = x.HandCards?.ToImmutableArray() ?? [],
                BestCards = [],
                CardsCategory = CardsCategory.None
            });

            return new ClientStateTable
            {
                SessionGuid = session.SessionGuid,
                TableID = session.Table.ID,
                PlayersMax = Settings.TablePlayersMax,

                LastTransactionID = lastTransaction.ID,
                LastTransactionSequence = lastTransaction.Sequence ?? 0,

                HandID = State.HandID,
                HandState = State.RoundID ?? HandState.NotInGame,
                IsChatEnabled = State.Common.IsChatEnabled,
                ShowPenaltyDialog = false,

                TableChangeEnabled = State.Common.TableChangeEnabled,
                MinPlayersWait = State.Common.MinPlayersWait,

                Players = [..players],
                CommunityCards = [],
                Pots = []
            };
        }

        public async Task StopAsync()
        {
            await TransactionAsync(new TableTransactionDto { TableID = TableKey.ID, TypeSub = TableTransactionType.Terminate });
            await _tableRepository.StopAsync(TableKey.ID);
        }

        public async Task JoinAsync(PlayerKey playerKey, SessionRoomKey sessionRoomKey, string nickname, SessionTableKey sessionTableKey, Guid sessionGuid)
        {
            await Players.TableJoinAsync(playerKey, sessionRoomKey, nickname, sessionTableKey, sessionGuid);
        }

        public async Task SitInAsync(PlayerKey playerKey, byte place)
        {
            await Players.TableSitInAsync(playerKey, place);
        }

        public async Task SitOutAsync(PlayerKey playerKey)
        {
            await Players.TableSitOutAsync(playerKey);
        }

        public async Task RechargeAsync(PlayerKey playerKey, decimal credit)
        {
            await Players.TableRechargeAsync(playerKey, credit);
        }

        public async Task LeaveAsync(PlayerKey playerKey)
        {
            await Players.TableLeaveAsync(playerKey);
        }

        public async Task ChangeAsync(PlayerKey playerKey, decimal credit)
        {
            await Players.TableChangeAsync(playerKey, credit);
        }

        public async Task ChangeEnableAsync()
        {
            await Players.TableChangeEnableAsync();
        }

        public async Task BlindsAsync(TableBlindsUpdate blinds)
        {
            if (Settings.Ante != blinds.Ante || Settings.SmallBlind != blinds.SmallBlind || Settings.BigBlind != blinds.BigBlind)
            {
                Settings.Ante = blinds.Ante;
                Settings.SmallBlind = blinds.SmallBlind;
                Settings.BigBlind = blinds.BigBlind;
                Settings.LimitLow = blinds.BigBlind;
                Settings.LimitHigh = blinds.BigBlind * 2;

                await _tableRepository.UpdateBlindsAsync(TableKey.ID, Settings.RulesType, Settings.Ante, Settings.SmallBlind, Settings.BigBlind, Settings.LimitLow, Settings.LimitHigh);

                if (State.Players.Count > 0)
                {
                    await TransactionAsync(new TableTransactionDto
                    {
                        TableID = TableKey.ID,
                        TypeSub = TableTransactionType.ChangedBlinds,
                        DateTimePlan = blinds.NextBlindChange?.LocalDateTime,
                        Blinds = new TableTransactionBlindsDto
                        {
                            CurrentAnte = blinds.Ante,
                            CurrentBigBlind = blinds.BigBlind,
                            CurrentSmallBlind = blinds.SmallBlind,
                            NextAnte = blinds.NextAnte,
                            NextBigBlind = blinds.NextBigBlind,
                            NextSmallBlind = blinds.NextSmallBlind
                        }
                    });
                }

                _log.LogInformation("Blinds: BigBlind={BigBlind}, SmallBlind={SmallBlind}, Ante={Ante}", Settings.BigBlind, Settings.SmallBlind, Settings.Ante);
            }

            if (!State.Common.AutoPayBlind)
            {
                await State.UpdateAsync(x =>
                {
                    x.UpdateCommon(y => y.AutoPayBlind = true);
                });
            }
        }

        public async Task TournamentLooseAsync(PlayerKey playerKey, int rank)
        {
            if (State.TryGetPlayer(playerKey, out var player))
            {
                await TransactionAsync(new SitTransactionDto
                {
                    TableID = player.Table.ID,
                    TypeSub = SitTransactionType.TournamentLooser,
                    PlayerID = player.Player.ID,
                    Nickname = player.Nickname,
                    SessionGuid = player.SessionGuid,
                    PlayerRank = rank
                });
            }
        }

        public async Task TournamentWinAsync(PlayerKey playerKey, int? rank)
        {
            if (State.TryGetPlayer(playerKey, out var player))
            {
                await TransactionAsync(new SitTransactionDto
                {
                    TableID = player.Table.ID,
                    TypeSub = SitTransactionType.TournamentWinner,
                    PlayerID = player.Player.ID,
                    Nickname = player.Nickname,
                    SessionGuid = player.SessionGuid,
                    PlayerRank = rank
                });
            }
        }

        public async Task<int> HandStartAsync(ImmutableArray<TableHandStartPlayer> players)
        {
            return await _tableHandProcessor.HandStartAsync(this, players);
        }

        public async Task HandFinishAsync(ImmutableArray<TableHandFinishPlayer> players)
        {
            await _tableHandProcessor.HandFinishAsync(this, players);
        }

        public async Task<TimeSpan> HandMinPlayersReachedAsync()
        {
            return await _tableHandProcessor.HandMinPlayersReachedAsync(this);
        }

        public async Task HandMinPlayersWaitAsync()
        {
            await _tableHandProcessor.HandMinPlayersWaitAsync(this);
        }

        public Task TimeoutAsync(PlayerKey playerKey, Guid queueKey)
        {
            if (State.TryGetPlayer(playerKey, out var player) && player.Turn?.QueueKey == queueKey)
            {
                _queueService.Queue(new QueueActionRoomPlayerSitOut(RoomKey, player.TablePlayer));
            }
            return Task.CompletedTask;
        }

        public async Task<bool> RequestMessageAsync(PlayerKey playerKey, string message)
        {
            return await _tableRequestProcessor.MessageAsync(this, playerKey, message);
        }

        public async Task<bool> RequestAction(PlayerKey playerKey, StepTransactionAction action, decimal? amount)
        {
            return await _tableRequestProcessor.ActionAsync(this, playerKey, action, amount);
        }

        public async Task<long> TransactionAsync(TransactionDto transaction)
        {
            return await _tableTransactionProcessor.PublishAsync(this, transaction);
        }

        public async Task ProcessPhaseAsync()
        {
            while (await _tablePhaseProcessor.ProcessAsync()) { }
        }
    }
}