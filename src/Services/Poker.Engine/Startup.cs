using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Orleans.Hosting;
using Orleans.Storage;
using Poker.Common.Data;
using Poker.Common.Infrastructure;
using Poker.Common.Infrastructure.Settings;
using Poker.Common.Logic;
using Poker.Common.Logic.Platform;
using Poker.Common.Logic.Platform.Reconciliation;
using Poker.Common.Logic.Platform.ResponsibleGaming;
using Poker.Common.Logic.Tournaments;
using Poker.Engine.CashGames;
using Poker.Engine.Interface.Messaging;
using Poker.Engine.Interface.Platform;
using Poker.Engine.Interface.Tournaments;
using Poker.Engine.Jobs;
using Poker.Engine.Queue;
using Poker.Engine.Rooms;
using Poker.Engine.Tables;
using Poker.Engine.Tables.Phases;
using Poker.Engine.Tables.Players;
using Poker.Engine.Tournaments;
using System;

namespace Poker.Engine
{
    public static class Startup
    {
        public static void Main(string[] args)
        {
            var host = Host.CreateApplicationBuilder(args);

            host.Services.AddWindowsService();
            host.ConfigurePokerInfrastructure();
            if (!new PokerServicesSettings(host.Configuration).EngineSiloEnabled)
            {
                host.AddPokerDataEF();
                var idle = host.Build();
                var idleLog = idle.Services.GetRequiredService<ILoggerFactory>().CreateLogger(typeof(Startup).FullName ?? nameof(Startup));
                idleLog.LogInformation("New engine disabled");
                idle.Run();
                return;
            }

            ConfigureServices(host);

            host.UseOrleans(silo =>
            {
                ConfigureSilo(silo);

                var settings = new PokerServicesSettings(host.Configuration);
                var uri = new Uri(settings.EngineSiloUrl);
                silo.ConfigureEndpoints(uri.Port + 1, uri.Port);
                silo.UseLocalhostClustering(uri.Port + 1, uri.Port);
#pragma warning disable ORLEANSEXP003
                silo.AddDistributedGrainDirectory();
#pragma warning restore ORLEANSEXP003
            });

            var app = host.Build();
            app.Run();
        }

        public static void ConfigureSilo(ISiloBuilder silo)
        {
            silo.Services.AddSingleton<IGrainStorage, CustomGrainStorageProvider>();
        }

        public static void ConfigureServices(IHostApplicationBuilder host)
        {
            host.AddPokerDataEF();
            host.Services.AddPokerLogic();
            ConfigurePlatform(host);
            ConfigureTournaments(host);
            ConfigureEngine(host);
        }

        private static void ConfigurePlatform(IHostApplicationBuilder host)
        {
            host.Services.AddSingleton<IResponsibleGamingApiClient, ResponsibleGamingApiClient>();
            host.Services.AddSingleton<OpenIDConnectTokenService>();
            host.Services.AddSingleton<MockPlatformService>();
            host.Services.AddSingleton<EBetPlatformService>();
            host.Services.AddSingleton<NikePlatformService>();
            host.Services.AddTransient<IPlatformService, PlatformGrainService>();
            host.Services.AddTransient<IPlatformServiceProxy, PlatformGrainService>();
            host.Services.AddHostedService<IReconciliationRequestService, ReconciliationRequestService>();
            host.Services.AddHostedService<ReconciliationAttemptService, ReconciliationAttemptService>();
        }

        private static void ConfigureTournaments(IHostApplicationBuilder host)
        {
            host.Services.AddTransient<ITournamentRegisterService, TournamentRegisterService>();
            host.Services.AddTransient<ITournamentEvaluationService, TournamentEvaluationService>();
            host.Services.AddTransient<ITournamentPayoutService, TournamentPayoutService>();
            host.Services.AddTransient<ITournamentManagerService, TournamentManagerService>();
            host.Services.AddTransient<ITournamentSchedulerService, TournamentSchedulerService>();
        }

        private static void ConfigureEngine(IHostApplicationBuilder host)
        {
            host.Services.AddSingleton<LiveUpdateStream>();
            host.Services.AddSingleton<TransactionsStream>();
            host.Services.AddTransient<TableHandProcessor>();
            host.Services.AddTransient<TablePhaseProcessor>();
            host.Services.AddTransient<TablePlayersProcessor>();
            host.Services.AddTransient<TableRequestProcessor>();
            host.Services.AddTransient<TableTransactionProcessor>();
            host.Services.AddTransient<RoomPlayersProcessor>();
            host.Services.AddTransient<CashGameTableProcessor>();
            host.Services.AddTransient<CashGameRechargeService>();
            host.Services.AddTransient<CashGameButtonRotator>();
            host.Services.AddTransient<FastCashGameRankingService>();
            host.Services.AddTransient<FastCashGameButtonRotator>();
            host.Services.AddTransient<TournamentButtonRotator>();
            host.Services.AddTransient<TournamentTableProcessor>();
            host.Services.AddTransient<TournamentStateProcessor>();
            host.Services.AddHostedService<IQueueService, QueueService>();
            host.Services.AddHostedService<JobsService>();
        }
    }
}