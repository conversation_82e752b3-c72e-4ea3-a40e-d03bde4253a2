using Microsoft.Extensions.Logging;
using Orleans;
using Orleans.Runtime;
using Poker.Common.Data.Interface.Enums;
using Poker.Common.Data.Interface.Models.Sessions;
using Poker.Engine.Interface;
using Poker.Engine.Interface.Rooms;
using Poker.Engine.Queue;
using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Threading;
using System.Threading.Tasks;

namespace Poker.Engine.Rooms
{
    public abstract class RoomGrain : Grain, IRoomGrain, IIncomingGrainCallFilter
    {
        private readonly RoomGrainStateWrapper _state;
        private readonly RoomTableProcessor _roomTableProcessor;
        private readonly Dictionary<string, object> _logState = new();

        protected RoomGrain(
            IPersistentState<RoomGrainState> state,
            ILogger log,
            RoomPlayersProcessor roomPlayersProcessor,
            RoomTableProcessor roomTableProcessor,
            IButtonRotator buttonRotator,
            IQueueService queueService
        )
        {
            _state = new RoomGrainStateWrapper(state, queueService);
            Log = log;
            Players = roomPlayersProcessor;
            _roomTableProcessor = roomTableProcessor;
            ButtonRotator = buttonRotator;
        }

        public RoomKey RoomKey { get; private set; }
        public IRoomGrainStateWrapper State => _state;
        protected ILogger Log { get; }
        protected RoomPlayersProcessor Players { get; }
        public IButtonRotator ButtonRotator { get; }

        public async Task Invoke(IIncomingGrainCallContext context)
        {
            using (Log.BeginScope(_logState))
            {
                await context.Invoke();
            }
        }

        public sealed override async Task OnActivateAsync(CancellationToken cancellationToken)
        {
            var roomID = (int)this.GetPrimaryKeyLong();
            var roomType = await OnInitializeAsync(roomID, cancellationToken);
            RoomKey = new(roomType, roomID);
            _logState[nameof(SessionTableInfoDto.RoomType)] = RoomKey.Type;
            _logState[nameof(SessionTableInfoDto.RoomID)] = RoomKey.ID;

            _state.Initialize();
            Players.Initialize(this);
        }

        protected abstract Task<RoomType> OnInitializeAsync(int roomID, CancellationToken cancellationToken);

        public async Task PingAsync()
        {
            foreach (var table in State.Tables)
            {
                try
                {
                    var grain = GrainFactory.GetTableGrain(table.Table);
                    await grain.PingAsync();
                }
                catch (Exception ex)
                {
                    Log.LogError(ex, "Ping error: TableID={TableID}", table.Table);
                }
            }
        }

        public Task<RoomGrainState> GetStateAsync()
        {
            return Task.FromResult(_state.State);
        }

        public async Task SessionConnectedAsync(SessionRoomKey session)
        {
            await Players.SessionConnectedAsync(session);
        }

        public async Task SessionDisconnectedAsync(SessionRoomKey session)
        {
            await Players.SessionDisconnectedAsync(session);
        }

        public async Task SessionDisconnectAsync(SessionRoomKey session, Guid queueKey)
        {
            await Players.SessionDisconnectAsync(session, queueKey);
        }

        public virtual async Task<bool> TablePlayerSitAsync(IRoomTablePlayerState tablePlayer, bool sitIn, byte? place = null)
        {
            await Players.TableSitAsync(tablePlayer.TablePlayer, sitIn, place);
            return true;
        }

        public virtual async Task TablePlayerLeaveAsync(TablePlayerKey tablePlayerKey)
        {
            await Players.TableLeaveAsync(tablePlayerKey);
        }

        public virtual Task TablePlayerChangeAsync(TablePlayerKey tablePlayerKey, decimal credit)
        {
            return Task.CompletedTask;
        }

        public virtual async Task TableHandStornoAsync(TableKey tableKey)
        {
            await _roomTableProcessor.HandStornoAsync(tableKey);
        }

        public virtual async Task TableHandFinishAsync(TableKey tableKey, ImmutableArray<RoomTableHandFinishPlayer> players)
        {
            await _roomTableProcessor.HandFinishAsync(tableKey, players);
        }

        public virtual async Task TableHandTryStartAsync(TableKey tableKey)
        {
            await _roomTableProcessor.HandTryStartAsync(tableKey);
        }

        public async Task<bool> RequestSitInAsync(TablePlayerKey tablePlayerKey, byte? place = null)
        {
            if (!_state.TryGetTablePlayer(tablePlayerKey, out var tablePlayer, out _))
            {
                Log.LogInformation("RequestSitIn player not at table");
                return false;
            }

            if (tablePlayer.SitIn)
            {
                Log.LogInformation("RequestSitIn player already sitting");
                return false;
            }

            return await TablePlayerSitAsync(tablePlayer, true, tablePlayer.Place ?? place.Value);
        }

        public async Task<bool> RequestSitOutAsync(TablePlayerKey tablePlayerKey)
        {
            if (!_state.TryGetTablePlayer(tablePlayerKey, out var tablePlayer, out _) || tablePlayer.Place is null)
            {
                Log.LogInformation("RequestSitOut player not at table");
                return false;
            }

            if (!tablePlayer.SitIn)
            {
                Log.LogInformation("RequestSitOut player already sitting out");
                return false;
            }

            await TablePlayerSitAsync(tablePlayer, false);
            return true;
        }

        public async Task<bool> RequestLeaveAsync(TablePlayerKey tablePlayerKey)
        {
            if (!_state.TryGetTablePlayer(tablePlayerKey, out _, out _))
            {
                Log.LogInformation("RequestLeave player not at table");
                return false;
            }

            await TablePlayerLeaveAsync(tablePlayerKey);
            return true;
        }
    }
}