using Poker.Engine.Interface;
using Poker.Engine.Interface.Rooms;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Poker.Engine.Rooms
{
    public interface IRoomGrainStateWrapper
    {
        IRoomState Room { get; }
        IReadOnlyCollection<IRoomPlayerState> SessionsActive { get; }
        IReadOnlyCollection<IRoomPlayerState> SessionsEliminated { get; }
        IReadOnlyCollection<IRoomTableState> Tables { get; }

        int SessionsCountSeated { get; }
        int SessionsCountJoined { get; }
        int PlayersCountJoined { get; }

        Task UpdateAsync(Action<RoomGrainStateWrapper> update);

        bool TryGetPlayer(<PERSON><PERSON><PERSON> key, out IRoomPlayerState player);
        bool TryGetSession(SessionRoom<PERSON>ey key, out IRoomPlayerState player);
        bool TryGetSessionTablePlayers(SessionRoom<PERSON>ey key, out IReadOnlyCollection<IRoomTablePlayerState> tablePlayers);

        bool TryGetTablePlayer(SessionR<PERSON><PERSON><PERSON> key, out IRoomTablePlayerState tablePlayer);
        bool TryGetTablePlayer(TablePlayer<PERSON>ey key, out IRoomTablePlayerState tablePlayer, out IRoomPlayerState player);

        bool TryGetTable(TableKey key, out IRoomTableState table, out IReadOnlyCollection<IRoomTablePlayerState> tablePlayers);
        bool PlaceTaken(byte place);
        TableKey FindTable(int playerCount);
    }
}