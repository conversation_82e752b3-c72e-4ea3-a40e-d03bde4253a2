using Orleans.Runtime;
using Poker.Engine.Interface;
using Poker.Engine.Interface.Rooms;
using Poker.Engine.Queue;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Poker.Engine.Rooms
{
    public class RoomGrainStateWrapper : IRoomGrainStateWrapper
    {
        private readonly IPersistentState<RoomGrainState> _state;
        private readonly Dictionary<PlayerKey, HashSet<RoomPlayerState>> _players = [];
        private readonly Dictionary<TableKey, HashSet<RoomTablePlayerState>> _tablePlayers = [];
        private readonly Dictionary<SessionRoomKey, HashSet<RoomTablePlayerState>> _sessionTables = [];
        private readonly Dictionary<SessionRoomKey, RoomPlayerState> _sessionsActive = [];
        private readonly Dictionary<SessionRoomKey, RoomPlayerState> _sessionsEliminated = [];
        private readonly IQueueService _queueService;

        public RoomGrainStateWrapper(IPersistentState<RoomGrainState> state, IQueueService queueService)
        {
            _state = state;
            _queueService = queueService;
        }

        public RoomGrainState State => _state.State;
        public IRoomState Room => _state.State.Room;

        public IReadOnlyCollection<IRoomPlayerState> SessionsActive => _sessionsActive.Values;
        public IReadOnlyCollection<IRoomPlayerState> SessionsEliminated => _sessionsEliminated.Values;
        public IReadOnlyCollection<IRoomTableState> Tables => _state.State.Tables.Values;

        public int SessionsCountSeated => _tablePlayers.SelectMany(x => x.Value).Count(x => x.Place is not null);
        public int SessionsCountJoined => _state.State.Sessions.Count(x => x.Value.HasJoinedTable || x.Value.Credit > 0);
        public int PlayersCountJoined => _state.State.Sessions.Where(x => x.Value.HasJoinedTable).DistinctBy(x => x.Value.Player).Count();

        public void Initialize()
        {
            foreach (var tables in _state.State.Tables.Values)
            {
                _tablePlayers.Add(tables.Table, []);
            }
            foreach (var session in _state.State.Sessions.Values)
            {
                if (!_players.TryGetValue(session.Player, out var sessions))
                {
                    sessions = [];
                    _players.Add(session.Player, sessions);
                }
                sessions.Add(session);
                (session.Eliminated is null ? _sessionsActive : _sessionsEliminated).Add(session.SessionRoom, session);
                _sessionTables.Add(session.SessionRoom, []);
            }
            foreach (var tablePlayer in _state.State.TablePlayers.Values)
            {
                _tablePlayers[tablePlayer.Table].Add(tablePlayer);
                _sessionTables[tablePlayer.SessionRoom].Add(tablePlayer);
            }
        }

        async Task IRoomGrainStateWrapper.UpdateAsync(Action<RoomGrainStateWrapper> update)
        {
            update(this);
            await _state.WriteStateAsync();
        }

        public bool TryGetPlayer(PlayerKey key, out IRoomPlayerState player)
        {
            if (_players.TryGetValue(key, out var sessions) && sessions.Count == 1)
            {
                player = sessions.Single();
                return true;
            }
            player = null;
            return false;
        }

        public bool TryGetSession(SessionRoomKey key, out IRoomPlayerState player)
        {
            if (_state.State.Sessions.TryGetValue(key, out var session))
            {
                player = session;
                return true;
            }
            player = null;
            return false;
        }

        public bool TryGetSessionTablePlayers(SessionRoomKey key, out IReadOnlyCollection<IRoomTablePlayerState> tablePlayers)
        {
            if (_sessionTables.TryGetValue(key, out var players))
            {
                tablePlayers = players;
                return true;
            }
            tablePlayers = null;
            return false;
        }

        public bool TryGetTablePlayer(SessionRoomKey key, out IRoomTablePlayerState tablePlayer)
        {
            if (_state.State.Sessions.TryGetValue(key, out var session))
            {
                if (session.Table is not null && _state.State.TablePlayers.TryGetValue(new TablePlayerKey(session.Table.Value, session.Player), out var tablePlayerState))
                {
                    tablePlayer = tablePlayerState;
                    return true;
                }
            }
            tablePlayer = null;
            return false;
        }

        public bool TryGetTablePlayer(TablePlayerKey key, out IRoomTablePlayerState tablePlayer, out IRoomPlayerState player)
        {
            if (_state.State.TablePlayers.TryGetValue(key, out var tablePlayerState))
            {
                if (_state.State.Sessions.TryGetValue(tablePlayerState.SessionRoom, out var session))
                {
                    tablePlayer = tablePlayerState;
                    player = session;
                    return true;
                }
            }
            tablePlayer = null;
            player = null;
            return false;
        }

        public bool TryGetTable(TableKey key, out IRoomTableState table, out IReadOnlyCollection<IRoomTablePlayerState> tablePlayers)
        {
            if (_state.State.Tables.TryGetValue(key, out var state))
            {
                table = state;
                tablePlayers = _tablePlayers.TryGetValue(key, out var players) ? players : [];
                return true;
            }
            table = null;
            tablePlayers = null;
            return false;
        }

        public bool PlaceTaken(byte place)
        {
            return _tablePlayers.Any(x => x.Value.Any(y => y.Place == place));
        }

        public TableKey FindTable(int playerCount)
        {
            var minTable = (TableKey?)null;
            var minCount = 0;

            foreach (var table in _tablePlayers.OrderBy(x => x.Key))
            {
                if (table.Value.Count < playerCount)
                {
                    return table.Key;
                }
                if (minTable is null || table.Value.Count < minCount)
                {
                    minTable = table.Key;
                    minCount = table.Value.Count;
                }
            }
            return minTable ?? _tablePlayers.First().Key;
        }

        public void AddTable(TableKey key)
        {
            _state.State.Tables.Add(key, new RoomTableState
            {
                Table = key,
                TableChangeEnabled = _state.State.Room.TableChangeEnabled
            });
            _tablePlayers.Add(key, []);
        }

        public void RemoveTable(IRoomTableState table)
        {
            _state.State.Tables.Remove(table.Table);
            _tablePlayers.Remove(table.Table);
        }

        public void AddSession(RoomPlayerState session)
        {
            if (!_players.TryGetValue(session.Player, out var sessions))
            {
                sessions = [];
                _players.Add(session.Player, sessions);
            }
            sessions.Add(session);
            _state.State.Sessions.Add(session.SessionRoom, session);
            (session.Eliminated is null ? _sessionsActive : _sessionsEliminated).Add(session.SessionRoom, session);
            _sessionTables.Add(session.SessionRoom, []);
        }

        public void RemoveSession(IRoomPlayerState player)
        {
            if (_state.State.Sessions.TryGetValue(player.SessionRoom, out var state))
            {
                if (_players.TryGetValue(player.Player, out var sessions))
                {
                    sessions.Remove(state);
                    if (sessions.Count == 0)
                    {
                        _players.Remove(player.Player);
                    }
                }
                _state.State.Sessions.Remove(player.SessionRoom);
            }
            _sessionsActive.Remove(player.SessionRoom);
            _sessionsEliminated.Remove(player.SessionRoom);
            _sessionTables.Remove(player.SessionRoom);
        }

        public void AddTablePlayer(RoomTablePlayerState tablePlayer)
        {
            _tablePlayers[tablePlayer.Table].Add(tablePlayer);
            _sessionTables[tablePlayer.SessionRoom].Add(tablePlayer);
            _state.State.TablePlayers.Add(tablePlayer.TablePlayer, tablePlayer);
        }

        public void RemoveTablePlayer(IRoomTablePlayerState tablePlayer)
        {
            if (_state.State.TablePlayers.TryGetValue(tablePlayer.TablePlayer, out var state))
            {
                _tablePlayers[tablePlayer.Table].Remove(state);
                _sessionTables[tablePlayer.SessionRoom].Remove(state);
                _state.State.TablePlayers.Remove(tablePlayer.TablePlayer);
            }
        }

        public void UpdateSession(IRoomPlayerState player, Action<RoomPlayerState> update)
        {
            if (_state.State.Sessions.TryGetValue(player.SessionRoom, out var session))
            {
                UpdateSession(session, update);
            }
        }

        public void UpdateSession(SessionRoomKey sessionKey, Action<RoomPlayerState> update)
        {
            if (_state.State.Sessions.TryGetValue(sessionKey, out var session))
            {
                UpdateSession(session, update);
            }
        }

        private void UpdateSession(RoomPlayerState session, Action<RoomPlayerState> update)
        {
            var eliminated = session.Eliminated is not null;
            var observerQueueKey = session.ObserverQueueKey;
            var disconnectQueueKey = session.DisconnectQueueKey;
            var idleQueueKey = session.IdleQueueKey;
            var rechargeQueueKey = session.RechargeQueueKey;

            update(session);

            if (session.Eliminated is not null != eliminated)
            {
                (session.Eliminated is null ? _sessionsEliminated : _sessionsActive).Remove(session.SessionRoom);
                (session.Eliminated is null ? _sessionsActive : _sessionsEliminated).Add(session.SessionRoom, session);
            }

            if (observerQueueKey is not null && session.ObserverQueueKey != observerQueueKey)
            {
                _queueService.Cancel(observerQueueKey.Value);
            }
            if (disconnectQueueKey is not null && session.DisconnectQueueKey != disconnectQueueKey)
            {
                _queueService.Cancel(disconnectQueueKey.Value);
            }
            if (idleQueueKey is not null && session.IdleQueueKey != idleQueueKey)
            {
                _queueService.Cancel(idleQueueKey.Value);
            }
            if (rechargeQueueKey is not null && session.RechargeQueueKey != rechargeQueueKey)
            {
                _queueService.Cancel(rechargeQueueKey.Value);
            }
        }

        public void UpdateTablePlayer(IRoomTablePlayerState tablePlayer, Action<RoomTablePlayerState> update)
        {
            if (_state.State.TablePlayers.TryGetValue(tablePlayer.TablePlayer, out var state))
            {
                update(state);
            }
        }

        public void UpdateTable(IRoomTableState table, Action<RoomTableState> update)
        {
            if (_state.State.Tables.TryGetValue(table.Table, out var state))
            {
                update(state);
            }
        }

        public void UpdateRoom(Action<RoomState> update)
        {
            update(_state.State.Room);
        }
    }
}