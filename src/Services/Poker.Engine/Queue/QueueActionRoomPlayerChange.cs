using Orleans;
using Poker.Engine.Interface;
using System.Threading.Tasks;

namespace Poker.Engine.Queue
{
    public record QueueActionRoomPlayerChange(RoomKey Room, TablePlayerKey TablePlayer, decimal credit) : IQueueActionImmediate
    {
        public async Task ExecuteAsync(IGrainFactory grainFactory)
        {
            var grain = grainFactory.GetRoomGrain(Room);
            await grain.TablePlayerChangeAsync(TablePlayer, credit);
        }
    }

    public record QueueActionRoomPlayerSitOut(RoomKey Room, TablePlayerKey TablePlayer) : IQueueActionImmediate
    {
        public async Task ExecuteAsync(IGrainFactory grainFactory)
        {
            var grain = grainFactory.GetRoomGrain(Room);
            await grain.RequestSitOutAsync(TablePlayer);
        }
    }
}