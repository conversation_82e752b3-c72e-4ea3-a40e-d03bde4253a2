using Orleans;
using System;
using System.Collections.Immutable;
using System.Threading.Tasks;

namespace Poker.Engine.Interface.Rooms
{
    public interface IRoomGrain : IGrainWithIntegerKey
    {
        Task PingAsync();
        Task<RoomGrainState> GetStateAsync();

        Task SessionConnectedAsync(SessionRoomKey session);
        Task SessionDisconnectedAsync(SessionRoomKey session);
        Task SessionDisconnectAsync(SessionRoomKey session, Guid queueKey);

        Task TablePlayerChangeAsync(TablePlayerKey tablePlayerKey, decimal credit);

        Task TableHandStornoAsync(TableKey tableKey);
        Task TableHandFinishAsync(TableKey tableKey, ImmutableArray<RoomTableHandFinishPlayer> players);
        Task TableHandTryStartAsync(TableKey tableKey);

        Task<bool> RequestSitInAsync(TablePlayerKey tablePlayerKey, byte? place = null);
        Task<bool> RequestSitOutAsync(TablePlayerKey tablePlayerKey);
        Task<bool> RequestLeaveAsync(TablePlayerKey tablePlayerKey);
    }
}