using Orleans;
using System;
using System.Threading.Tasks;

namespace Poker.Engine.Interface.Messaging
{
    public class TransactionsStream : ITransactionsObserver
    {
        private readonly IClusterClient _clusterClient;
        private ITransactionsGrain _grain;
        private Func<TransactionMessageDto, Task> _callback;
        private ITransactionsObserver _subscription;

        public TransactionsStream(IClusterClient clusterClient)
        {
            _clusterClient = clusterClient;
        }

        private ITransactionsGrain Grain => _grain ??= _clusterClient.GetGrain<ITransactionsGrain>(0);

        public async Task SubscribeAsync(Func<TransactionMessageDto, Task> callback)
        {
            _callback = callback;
            _subscription = _clusterClient.CreateObjectReference<ITransactionsObserver>(this);
            await Grain.SubscribeAsync(_subscription);
        }

        public async Task UnsubscribeAsync()
        {
            if (_subscription is not null)
            {
                await Grain.UnsubscribeAsync(_subscription);
            }
        }

        public async Task ReceiveAsync(TransactionMessageDto message)
        {
            await _callback(message);
        }
    }
}