using Orleans;
using Poker.Common.Data.Interface.Enums;
using Poker.Common.Data.Interface.Models.Transactions;
using System;
using System.Collections.Immutable;
using System.Threading.Tasks;

namespace Poker.Engine.Interface.Tables
{
    public interface ITableGrain : IGrainWithIntegerKey
    {
        Task PingAsync();
        Task<TableGrainState> GetStateAsync();
        Task<ClientStateTable> GetClientStateAsync(PlayerKey playerKey);

        Task StopAsync();

        Task JoinAsync(<PERSON><PERSON><PERSON> playerK<PERSON>, SessionRoomKey sessionRoomKey, string nickname, SessionTableKey sessionTableKey, Guid sessionGuid);
        Task SitInAsync(PlayerKey playerKey, byte place);
        Task SitOutAsync(PlayerKey playerKey);
        Task RechargeAsync(PlayerKey playerKey, decimal credit);
        Task LeaveAsync(PlayerKey playerKey);

        Task ChangeAsync(PlayerKey playerKey, decimal credit);
        Task ChangeEnableAsync();

        Task BlindsAsync(TableBlindsUpdate blinds);
        Task TournamentLooseAsync(Player<PERSON>ey playerKey, int rank);
        Task TournamentWinAsync(Player<PERSON>ey playerKey, int? rank);

        Task<int> HandStartAsync(ImmutableArray<TableHandStartPlayer> players);
        Task HandFinishAsync(ImmutableArray<TableHandFinishPlayer> players);

        Task<TimeSpan> HandMinPlayersReachedAsync();
        Task HandMinPlayersWaitAsync();

        Task TimeoutAsync(PlayerKey playerKey, Guid queueKey);

        Task<bool> RequestMessageAsync(PlayerKey playerKey, string message);
        Task<bool> RequestAction(PlayerKey playerKey, StepTransactionAction action, decimal? amount);
        Task<long> TransactionAsync(TransactionDto transaction);
    }
}