using System;
using System.Threading;
using System.Threading.Tasks;

namespace Poker.Common.Infrastructure.Helpers
{
    public sealed class SemaphoreLock : IDisposable
    {
        private readonly SemaphoreSlim _semaphore;

        private SemaphoreLock(SemaphoreSlim semaphore)
        {
            _semaphore = semaphore;
        }

        public static IDisposable Lock(SemaphoreSlim semaphore)
        {
            semaphore.Wait();
            return new SemaphoreLock(semaphore);
        }

        public static async Task<IDisposable> LockAsync(SemaphoreSlim semaphore)
        {
            await semaphore.WaitAsync();
            return new SemaphoreLock(semaphore);
        }

        public void Dispose()
        {
            _semaphore.Release();
        }
    }
}