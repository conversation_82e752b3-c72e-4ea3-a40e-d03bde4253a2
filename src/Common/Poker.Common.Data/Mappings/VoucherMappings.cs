using Casino.Common.Server.Extensions;
using Poker.Common.Data.Models;
using Poker.Common.Data.Interface.Models.Vouchers;

namespace Poker.Common.Data.Mappings
{
    public class VoucherMappings : MappingProfile
    {
        public VoucherMappings()
        {
            CreateMap<TOR_VoucherProvider, VoucherProviderDto>()
                .ReverseMap()
                .ForMember(x => x.ID, x => x.Ignore());

            CreateMap<TOR_VoucherTemplate, VoucherTemplateDto>()
                .ForMember(x => x.RefundableTo, x => x.MapFrom(y => y.Date - y.RefundableTo))
                .ForMember(x => x.IssuableTo, x => x.MapFrom(y => y.Date - y.IssuableTo))
                .ForMember(x => x.VoucherProvider, x => x.ExplicitExpansion())
                .ForMember(x => x.Tournaments, x => x.MapFromExplicit(y => y.TOR_Tournaments))
                .ReverseMap()
                .ForMember(x => x.ID, x => x.Ignore())
                .ForMember(x => x.RefundableTo, x => x.MapFrom(y => y.Date - y.RefundableTo))
                .ForMember(x => x.IssuableTo, x => x.MapFrom(y => y.Date - y.IssuableTo))
                .ForMember(x => x.VoucherProviderID, x => x.MapFrom(y => y.VoucherProvider.ID))
                .ForMember(x => x.VoucherProvider, x => x.Ignore())
                .ForMember(x => x.TOR_Tournaments, x => x.Ignore());

            CreateMap<TOR_VoucherPurchase, VoucherPurchaseDto>()
                .ForMember(x => x.PlayerID, x => x.MapFrom(y => y.RppPlayerID))
                .ForMember(x => x.Nickname, x => x.MapFrom(y => y.TOR_TournamentPlayer.RppPlayer.Nickname))
                .ForMember(x => x.TournamentName, x => x.MapFrom(y => y.TOR_TournamentPlayer.Tournament.IDNavigation.Name))
                .ForMember(x => x.RefundableTo, x => x.MapFrom(y => y.Created + (y.VoucherTemplate.Date - y.VoucherTemplate.RefundableTo)))
                .ForMember(x => x.VoucherTemplate, x => x.ExplicitExpansion())
                .ReverseMap()
                .ForMember(x => x.RppPlayerID, x => x.MapFrom(y => y.PlayerID))
                .ForMember(x => x.ID, x => x.Ignore())
                .ForMember(x => x.VoucherTemplateID, x => x.MapFrom(y => y.VoucherTemplate.ID))
                .ForMember(x => x.VoucherTemplate, x => x.Ignore())
                .ForMember(x => x.Created, x => x.Ignore())
                .ForMember(x => x.TOR_TournamentPlayer, x => x.Ignore());
        }
    }
}