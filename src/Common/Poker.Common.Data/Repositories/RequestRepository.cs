using Casino.Common.NETStandard.Extensions;
using Casino.Common.Server.Sql;
using Casino.Common.Server.UnitOfWork;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Poker.Common.Data.Contexts;
using Poker.Common.Data.Interface.Enums;
using Poker.Common.Data.Interface.Models.Requests;
using Poker.Common.Data.Interface.Repositories;
using Poker.Common.Data.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Poker.Common.Data.Repositories
{
    public class RequestRepository : IRequestRepository
    {
        private readonly ILogger<RequestRepository> _log;
        private readonly IUnitOfWorkFactory<MultipokerDbContext> _unitOfWorkFactory;

        public RequestRepository(ILogger<RequestRepository> log, IUnitOfWorkFactory<MultipokerDbContext> unitOfWorkFactory)
        {
            _log = log;
            _unitOfWorkFactory = unitOfWorkFactory;
        }

        public async Task<bool> CreateWithSequenceAsync(RequestDto model, CancellationToken cancellationToken = default)
        {
            using var uow = _unitOfWorkFactory.Provide();

            model.Created = DateTime.Now;

            var call = new SqlProcedureCall("proc_PTH_RequestIns");
            var id = call.AddOutput<long>("@bigID");
            call.AddInput("@uqiSessionGuid", model.SessionGuid);
            call.AddInput("@tinType", (byte)model.Type);
            call.AddInput("@tinTypeSub", (byte?)model.TypeSub);
            call.AddInput("@strMessage", model.Message, 128);
            call.AddInput("@tinPlace", model.Place > 0 ? model.Place : null);
            call.AddInput("@curAmount", model.Amount);
            call.AddInput("@datCreated", model.Created);
            call.AddInput("@datFinished", model.Finished);
            call.AddInput("@bigTransactionID", model.TransactionID);
            var tableID = call.AddOutput<int>("@tableID");
            var playerID = call.AddOutput<int>("@playerID");
            var sequence = call.AddOutput<int?>("@sequence");

            await call.ExecuteAsync(uow.Context, cancellationToken);

            model.ID = id.GetValue();
            model.TableID = tableID.GetValue();
            model.PlayerID = playerID.GetValue();
            model.Sequence = sequence.GetValue();

            var returnValue = call.GetReturnValue();
            if (returnValue != 0)
            {
                _log.LogWarning("Create failed: Result={Result}, {@ObjModel}", returnValue, model);
                return false;
            }
            return true;
        }

        public async Task<bool> UpdateAsync(RequestDto model, CancellationToken cancellationToken = default)
        {
            using var uow = _unitOfWorkFactory.Provide();

            var call = new SqlProcedureCall("proc_PTH_RequestUpd");
            call.AddInput("@bigID", model.ID);
            call.AddInput("@datFinished", model.Finished);
            call.AddInput("@bigTransactionID", model.TransactionID);
            await call.ExecuteAsync(uow.Context, cancellationToken);

            return call.GetReturnValue() == 0;
        }

        public async Task<List<RequestDto>> ListAsync(RequestFilter filter, CancellationToken cancellationToken = default)
        {
            using var uow = _unitOfWorkFactory.Provide().WithNoLock();

            return await uow.Context.PTH_Requests
                .WhereIf(filter.FromID is not null, x => x.ID > filter.FromID)
                .WhereIf(filter.FromSequence is not null, x => x.Sequence > filter.FromSequence)
                .WhereIf(filter.TableID is not null, x => x.TableID == filter.TableID)
                .WhereIf(filter.Finished is false, x => x.Finished == null)
                .WhereIf(filter.Finished is true, x => x.Finished != null)
                .Select(x => new RequestDto
                {
                    ID = x.ID,
                    TableID = x.TableID,
                    PlayerID = x.PlayerID,
                    SessionGuid = x.SessionGuid,
                    Type = (RequestType)x.Type,
                    TypeSub = (RequestSubType?)x.TypeSub,
                    Message = x.Message,
                    Place = x.Place,
                    Amount = x.Amount,
                    Created = x.Created,
                    Finished = x.Finished,
                    TransactionID = x.TransactionID,
                    Sequence = x.Sequence
                })
                .OrderBy(x => x.ID)
                .ToListAsync(cancellationToken);
        }

        public async Task<long> CreateAsync(RequestDto model)
        {
            using var uow = _unitOfWorkFactory.Provide();

            var entity = new PTH_Request
            {
                TableID = model.TableID,
                PlayerID = model.PlayerID,
                SessionGuid = model.SessionGuid,
                Type = (byte)model.Type,
                TypeSub = (byte?)model.TypeSub,
                Message = model.Message?.Truncate(128),
                Place = model.Place <= 0 ? null : model.Place,
                Amount = model.Amount,
                TransactionID = model.TransactionID
            };

            uow.Context.PTH_Requests.Add(entity);
            await uow.Context.SaveChangesAsync();

            return entity.ID;
        }

        public async Task FinishAsync(long id)
        {
            using var uow = _unitOfWorkFactory.Provide();

            await uow.Context.PTH_Requests
                .Where(x => x.ID == id && x.Finished == null)
                .ExecuteUpdateAsync(x => x.SetProperty(y => y.Finished, DateTime.Now));
        }
    }
}