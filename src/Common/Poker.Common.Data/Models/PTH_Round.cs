using System;
using System.Collections.Generic;

namespace Poker.Common.Data.Models;

public partial class PTH_Round
{
    public int TableID { get; set; }

    public int HandID { get; set; }

    public byte ID { get; set; }

    public byte PlayersCount { get; set; }

    public virtual PTH_Hand PTH_Hand { get; set; }

    public virtual ICollection<PTH_HandCard> PTH_HandCards { get; set; } = new List<PTH_HandCard>();

    public virtual ICollection<PTH_PotTransaction> PTH_PotTransactions { get; set; } = new List<PTH_PotTransaction>();

    public virtual ICollection<PTH_RoundPlayer> PTH_RoundPlayers { get; set; } = new List<PTH_RoundPlayer>();
}