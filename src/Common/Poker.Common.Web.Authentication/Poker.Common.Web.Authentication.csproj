<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
        <LangVersion>latest</LangVersion>
        <SatelliteResourceLanguages>en;cs;sk</SatelliteResourceLanguages>
        <IsPackable>false</IsPackable>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Poker.Common.Web\Poker.Common.Web.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="EDF.Web.User" Version="25.12.14" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="9.0.8" />
    </ItemGroup>
</Project>