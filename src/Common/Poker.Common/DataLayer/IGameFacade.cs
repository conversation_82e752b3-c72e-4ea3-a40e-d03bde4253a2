using Poker.Common.BusinessFacades;
using Poker.Common.BusinessObjects;
using Poker.Common.BusinessObjects.Core;
using Poker.Common.BusinessObjects.Tournaments;
using Poker.Common.Data.Interface.Models.Transactions;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Poker.Common.DataLayer
{
    public interface IGameFacade
    {
        Task ExecuteCheckAndSitOutAsync(StepTransactionDto step, SitTransactionDto sit);
        Task ExecuteFoldAndSitOutAsync(StepTransactionDto step, SitTransactionDto sit);
        Task ExecuteGameActionTransactionAsync(StepTransactionDto step, Request request);
        Task ExecuteHandCardsDealAsync(List<HandCardTransactionDto> handCards);
        Task ExecuteHandStartAsync(ITableThread tableThread, Hand hand, TablePlayerCollection tablePlayers);
        Task<Session> ExecuteJoinTableAsync(TournamentPlayer tournamentPlayer, Table table);
        Task ExecutePotTransactionAsync(PotTransactionDto potTransaction);
        Task ExecuteRoundEndAsync(Hand hand, List<PotTransactionDto> potTransactions);
        Task ExecuteRoundStartAsync(Round round);
        Task ExecuteShowAsync(StepTransactionDto step, Request request, TablePlayer player);
        Task ExecuteSitOutMissedBlindPlayerAsync(TablePlayer player, SitTransactionDto sit);
        Task ExecuteSitTransactionAsync(SitTransactionDto sit, Request request);
        Task ExecuteStornoAsync(Table table, TablePlayer player);
        Task ExecuteWaitAndSitOutAsync(TablePlayer player, StepTransactionDto step, SitTransactionDto sit);
        Task ExecuteWaitTransactionAsync(StepTransactionDto step, Request request);
    }
}