using Poker.Common.Data.Interface.Models.Sessions;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Poker.Common.Logic.Interface.Services
{
    public interface ISessionInfoService
    {
        Task<SessionInfoDto> GetSessionAsync(int sessionID, CancellationToken cancellationToken = default);
        Task<SessionInfoDto> GetSessionAsync(Guid sessionGuid, CancellationToken cancellationToken = default);
        Task<SessionTableInfoDto> GetRoomAsync(int roomID, CancellationToken cancellationToken = default);
        Task<SessionTableInfoDto> GetTableAsync(int tableID, CancellationToken cancellationToken = default);
    }
}