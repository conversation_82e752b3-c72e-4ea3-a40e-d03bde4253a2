using Poker.Common.Data.Interface.Models.Players;
using System.Threading.Tasks;

namespace Poker.Common.Data.Interface.Repositories
{
    public interface IPlayerRepository
    {
        Task<PlayerDto> GetAsync(int playerID);
        Task<PlayerDto> GetAsync(string externalID);

        Task<PlayerDto> CreateOrUpdateAsync(int playerID, string nickname);
        Task<PlayerDto> CreateOrUpdateAsync(string externalID, string nickname);
        Task<int> CreateVirtualAsync(string nickname);
    }
}