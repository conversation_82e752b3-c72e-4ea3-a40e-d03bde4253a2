using Poker.Common.Data.Interface.Models.Requests;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Poker.Common.Data.Interface.Repositories
{
    public interface IRequestRepository
    {
        Task<bool> CreateWithSequenceAsync(RequestDto model, CancellationToken cancellationToken = default);
        Task<bool> UpdateAsync(RequestDto model, CancellationToken cancellationToken = default);
        Task<List<RequestDto>> ListAsync(RequestFilter filter, CancellationToken cancellationToken = default);

        Task<long> CreateAsync(RequestDto model);
        Task FinishAsync(long id);
    }
}