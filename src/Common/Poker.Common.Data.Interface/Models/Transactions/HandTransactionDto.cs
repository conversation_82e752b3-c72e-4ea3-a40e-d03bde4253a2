using Orleans;
using Poker.Common.Data.Interface.Enums;
using System;

namespace Poker.Common.Data.Interface.Models.Transactions
{
    [GenerateSerializer]
    public class HandTransactionDto : TransactionDto
    {
        public HandTransactionDto() { }

        public HandTransactionDto(int tableID, HandTransactionType type, int handID, decimal? commission = null)
        {
            Created = DateTime.Now;
            TableID = tableID;
            TypeSub = type;
            HandID = handID;
            Commission = commission;
        }

        public override TransactionType Type => TransactionType.HandTransaction;

        [Id(0)] public HandTransactionType TypeSub { get; set; }
        [Id(1)] public int HandID { get; set; }

        [Id(2)] public decimal? Commission { get; set; }
    }
}