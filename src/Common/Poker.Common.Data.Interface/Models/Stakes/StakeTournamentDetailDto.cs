using Poker.Common.Data.Interface.Enums;
using System;

namespace Poker.Common.Data.Interface.Models.Stakes
{
    public class StakeTournamentDetailDto
    {
        public required int TournamentID { get; init; }
        public required string Name { get; init; }
        public required int? StakeID { get; init; }

        public required TournamentPayType PayType { get; init; }
        public required TournamentType Type { get; init; }
        public required bool IsQualification { get; init; }
        public required int? PlayersMax { get; init; }

        public required DateTime? Started { get; init; }
        public required DateTime? RegistrationClosed { get; init; }
        public required DateTime? Finish { get; init; }
        public required TimeSpan? RebuyTimeLimit { get; init; }

        public required decimal Stake { get; init; }
        public required decimal? PrizePoolMin { get; init; }

        public required int? RegistrationCount { get; init; }
        public required decimal? RegistrationPrice { get; init; }
        public required decimal? RegistrationCommission { get; init; }

        public required int? RebuyCount { get; init; }
        public required decimal? RebuyPrice { get; init; }
        public required decimal? RebuyCommission { get; init; }

        public required int? AddonCount { get; init; }
        public required decimal? AddonPrice { get; init; }
        public required decimal? AddonCommission { get; init; }
    }
}