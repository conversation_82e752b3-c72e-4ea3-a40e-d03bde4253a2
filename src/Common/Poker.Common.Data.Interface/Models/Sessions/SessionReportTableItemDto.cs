using System;

namespace Poker.Common.Data.Interface.Models.Sessions
{
    public class SessionReportTableItemDto
    {
        public int ID { get; set; }
        public int TableID { get; set; }

        public DateTime Created { get; set; }
        public DateTime? Abandoned { get; set; }
        public DateTime? Finished { get; set; }

        public decimal CreditIn { get; set; }
        public decimal CreditOut { get; set; }
        public decimal CreditCurrent { get; set; }
    }
}