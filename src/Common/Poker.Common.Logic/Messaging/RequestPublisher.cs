using Microsoft.Extensions.Logging;
using Poker.Common.Data.Interface.Models.Requests;
using Poker.Common.Data.Interface.Repositories;
using Poker.Common.Infrastructure.Messaging;
using Poker.Common.Infrastructure.Performance;
using Poker.Common.Infrastructure.Settings;
using Poker.Common.Logic.Interface.Messaging;
using System.Threading.Tasks;

namespace Poker.Common.Logic.Messaging
{
    public class RequestPublisher : MessagePublisherBase<RequestDto>, IRequestPublisher
    {
        private readonly IRequestRepository _requestRepository;

        public RequestPublisher(ILogger<RequestPublisher> log, IPerformanceCounterService performanceCounterService, ISettings settings, IRequestRepository requestRepository)
            : base(log, performanceCounterService, settings.ExternalServices.RabbitMqFullUri, settings.ExternalServices.RabbitMqRequestExchange)
        {
            _requestRepository = requestRepository;
        }

        protected override PerformanceCategory PerformanceCategory => PerformanceCategory.MessagingRequests;

        protected override async Task<bool> SaveToDatabaseAsync(RequestDto message)
        {
            return await _requestRepository.CreateWithSequenceAsync(message);
        }
    }
}