using System;

namespace Poker.Common.Logic.Platform.Nike
{
    public class NikeUnregisterRequest
    {
        /// <summary>
        /// Unique ID of a transaction.
        /// </summary>
        public required long TransactionId { get; set; }

        /// <summary>
        /// Unique ID of the registration transaction to cancel.
        /// </summary>
        public required long OriginalTransactionId { get; set; }

        /// <summary>
        /// Amount of money to refund.
        /// </summary>
        public required decimal Stake { get; set; }

        /// <summary>
        /// Amount of money spent as a commission to refund.
        /// </summary>
        public required decimal Commission { get; set; }

        /// <summary>
        /// Unique ID of a player.
        /// </summary>
        public required int PlayerId { get; set; }

        /// <summary>
        /// Unique ID of a tournament.
        /// </summary>
        public required int TournamentId { get; set; }

        /// <summary>
        /// Type of cancellation.
        /// </summary>
        public required string CancelType { get; set; }

        /// <summary>
        /// Timestamp of the unregister request.
        /// </summary>
        public required DateTimeOffset Timestamp { get; set; }
    }
}