using Poker.Common.Data.Interface.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Poker.Common.Logic.Cards
{
    public class HandCardsBest
    {
        private readonly byte[] _ranks;
        private readonly HandCardInfo[] _cards;

        private HandCardsBest(HandCardInfo[] cards)
        {
            _cards = cards;
            _ranks = cards.Select(x => (byte)x.Rank).OrderBy(x => x).ToArray();
        }

        private CardCode[] _best;
        public CardCode[] Cards => _best ??= _cards.Select(x => x.Code).ToArray();

        public static HandCardsBest Compute(CardCode[] cards)
        {
            var cardInfos = cards.Select(x => new HandCardInfo(x)).ToArray();
            var combinations = GetCombinationsWithoutRepetitions(cardInfos);

            HandCardsBest best = null;
            foreach (var combination in combinations)
            {
                var current = new HandCardsBest(combination.ToArray());
                if (best is null || current.CompareTo(best) > 0)
                {
                    best = current;
                }
            }
            return best;
        }

        private static List<HandCardInfo[]> GetCombinationsWithoutRepetitions(HandCardInfo[] items)
        {
            var buffer = new HandCardInfo[5];
            var results = new List<HandCardInfo[]>(21);
            Generate(5, 0);
            return results;

            void Generate(int countCurrent, int start)
            {
                if (countCurrent == 0)
                {
                    results.Add(buffer.ToArray());
                    return;
                }

                for (var i = start; i < items.Length; i++)
                {
                    buffer[^countCurrent] = items[i];
                    Generate(countCurrent - 1, i + 1);
                }
            }
        }

        private CardsCategory? _category;
        public CardsCategory Category => _category ??= GetCategory();

        private CardsCategory GetCategory()
        {
            if (RoyalStraightFlush)
            {
                return CardsCategory.RoyalStraightFlush;
            }
            if (StraightFlush)
            {
                return CardsCategory.StraightFlush;
            }
            if (FourOfKind)
            {
                return CardsCategory.FourOfKind;
            }
            if (FullHouse)
            {
                return CardsCategory.FullHouse;
            }
            if (Flush)
            {
                return CardsCategory.Flush;
            }
            if (Straight)
            {
                return CardsCategory.Straight;
            }
            if (ThreeOfKind)
            {
                return CardsCategory.ThreeOfKind;
            }
            if (TwoPairs)
            {
                return CardsCategory.TwoPairs;
            }
            if (OnePair)
            {
                return CardsCategory.OnePair;
            }
            return CardsCategory.HighCard;
        }

        private byte _onePairValue;
        private bool? _onePair;
        private bool OnePair => _onePair ??= GetOnePair();

        private bool GetOnePair()
        {
            for (var i = 0; i < _ranks.Length - 1; i++)
            {
                if (_ranks[i] == _ranks[i + 1])
                {
                    _onePairValue = _ranks[i];
                    return true;
                }
            }
            return false;
        }

        private byte _twoPairsValue1;
        private byte _twoPairsValue2;
        private bool? _twoPairs;
        private bool TwoPairs => _twoPairs ??= GetTwoPairs();

        private bool GetTwoPairs()
        {
            var firstPair = false;
            for (var i = 0; i < _ranks.Length - 1; i++)
            {
                if (_ranks[i] == _ranks[i + 1])
                {
                    if (firstPair)
                    {
                        _twoPairsValue2 = _ranks[i];
                        return true;
                    }
                    _twoPairsValue1 = _ranks[i];

                    firstPair = true;
                    i++;
                }
            }
            return false;
        }

        private byte _threeOfKindValue;
        private byte _threeOfKindKickerValue1;
        private byte _threeOfKindKickerValue2;
        private bool? _threeOfKind;
        private bool ThreeOfKind => _threeOfKind ??= GetThreeOfKind();

        private bool GetThreeOfKind()
        {
            if (_ranks[0] == _ranks[2] || _ranks[1] == _ranks[3] || _ranks[2] == _ranks[4])
            {
                if (_ranks[0] == _ranks[2])
                {
                    _threeOfKindValue = _ranks[0];
                    _threeOfKindKickerValue1 = _ranks[3];
                    _threeOfKindKickerValue2 = _ranks[4];
                }
                else if (_ranks[1] == _ranks[3])
                {
                    _threeOfKindValue = _ranks[1];
                    _threeOfKindKickerValue1 = _ranks[0];
                    _threeOfKindKickerValue2 = _ranks[4];
                }
                else if (_ranks[2] == _ranks[4])
                {
                    _threeOfKindValue = _ranks[2];
                    _threeOfKindKickerValue1 = _ranks[0];
                    _threeOfKindKickerValue2 = _ranks[1];
                }

                return true;
            }
            return false;
        }

        private bool? _straight;
        private bool Straight => _straight ??= GetStraight();

        private bool GetStraight()
        {
            for (var i = 0; i < _ranks.Length - 1; i++)
            {
                if (_ranks[i] != _ranks[i + 1] - 1)
                {
                    // nutne osetrenie na nizku postupku zacinajucu Esom, tzn. posledna karta (Eso) sa lisi
                    if (i == _ranks.Length - 2 && _ranks[0] == (byte)CardCodeRank.Two && _ranks[i + 1] == (byte)CardCodeRank.Ace)
                    {
                        // ide o nizku postupku zacinajucu Esom
                    }
                    else
                    {
                        return false;
                    }
                }
            }
            return true;
        }

        private bool? _flush;
        private bool Flush => _flush ??= GetFlush();

        private bool GetFlush()
        {
            for (var i = 0; i < _cards.Length - 1; i++)
            {
                if (_cards[i].Suit != _cards[i + 1].Suit)
                {
                    return false;
                }
            }
            return true;
        }

        private byte _fullHouseThreeOfKindValue;
        private byte _fullHousePairValue;
        private bool? _fullHouse;
        private bool FullHouse => _fullHouse ??= GetFullHouse();

        private bool GetFullHouse()
        {
            if ((_ranks[0] != _ranks[2] && _ranks[0] == _ranks[1] && _ranks[2] == _ranks[4]) ||
                (_ranks[0] != _ranks[3] && _ranks[0] == _ranks[2] && _ranks[3] == _ranks[4]))
            {
                // Dvojica + Trojica
                if (_ranks[0] != _ranks[2] && _ranks[0] == _ranks[1] && _ranks[2] == _ranks[4])
                {
                    _fullHousePairValue = _ranks[0];
                    _fullHouseThreeOfKindValue = _ranks[2];
                }
                // Trojica + Dvojica
                else
                {
                    _fullHouseThreeOfKindValue = _ranks[0];
                    _fullHousePairValue = _ranks[3];
                }

                return true;
            }
            return false;
        }

        private byte _fourOfKindValue;
        private bool? _fourOfKind;
        private bool FourOfKind => _fourOfKind ??= GetFourOfKind();

        private bool GetFourOfKind()
        {
            if (_ranks[0] == _ranks[3] || _ranks[1] == _ranks[4])
            {
                _fourOfKindValue = _ranks[0] == _ranks[3]
                    ? _ranks[0]
                    : _ranks[1];

                return true;
            }
            return false;
        }

        private bool StraightFlush => Straight && Flush;

        private bool? _royalStraightFlush;
        private bool RoyalStraightFlush => _royalStraightFlush ??= GetRoyalStraightFlush();

        private bool GetRoyalStraightFlush()
        {
            var hasTwo = false;
            var hasAce = false;

            if (Straight && Flush)
            {
                foreach (var card in _cards)
                {
                    if (card.Rank == CardCodeRank.Ace)
                    {
                        hasAce = true;
                    }

                    if (card.Rank == CardCodeRank.Two)
                    {
                        hasTwo = true;
                    }
                }

                if (hasAce && !hasTwo)
                {
                    return true;
                }
            }
            return false;
        }

        private int? _level1;
        public int Level1 => _level1 ??= GetLevel1();

        private int GetLevel1()
        {
            var result = 0;

            if (RoyalStraightFlush) { }
            else if (StraightFlush)
            {
                // Level1 = poslední (nejvyšší) karty postupky
                result = _ranks[^1];

                // Postupka, ktora zacina Esom
                if (_ranks[0] == (byte)CardCodeRank.Two && _ranks[^1] == (byte)CardCodeRank.Ace)
                {
                    result = _ranks[^2];
                }
            }
            else if (FourOfKind)
            {
                // Level1 = hodnota, ze které je složena kombinace Four of a kind
                result = _fourOfKindValue;
            }
            else if (FullHouse)
            {
                // Level1 = Hodnota trojice
                result = _fullHouseThreeOfKindValue;
            }
            else if (Flush)
            {
                // Level1 = (SELECT SUM(power(2,Value))
                result = CardsBitmap;
            }
            else if (Straight)
            {
                // Level1 = (SELECT SUM(power(2,Value)) alebo (SELECT SUM(power(2,Value2)) v pripade ze ide o nizku postupku

                // Postupka, ktora zacina Esom
                if (_ranks[0] == (byte)CardCodeRank.Two && _ranks[^1] == (byte)CardCodeRank.Ace)
                {
                    result = CardsBitmap2;
                }
                else
                {
                    result = CardsBitmap;
                }
            }
            else if (ThreeOfKind)
            {
                // Level1 = hodnota trojice
                result = _threeOfKindValue;
            }
            else if (TwoPairs)
            {
                // Level1 = hodnota vyssieho paru
                result = Math.Max(_twoPairsValue1, _twoPairsValue2);
            }
            else if (OnePair)
            {
                // Level1 = hodnota paru
                result = _onePairValue;
            }
            else
            {
                result = CardsBitmap;
            }

            return result;
        }

        private int? _level2;
        public int Level2 => _level2 ??= GetLevel2();

        private int GetLevel2()
        {
            var result = 0;

            if (RoyalStraightFlush) { }
            else if (StraightFlush) { }
            else if (FourOfKind)
            {
                // Level2 = hodnota doplňkové karty ke kombinaci Four of a kind
                foreach (var card in _cards)
                {
                    if ((byte)card.Rank != _fourOfKindValue)
                    {
                        result = (byte)card.Rank;
                        break;
                    }
                }
            }
            else if (FullHouse)
            {
                // Level2 = hodnota dvojice
                result = _fullHousePairValue;
            }
            else if (Flush) { }
            else if (Straight) { }
            else if (ThreeOfKind)
            {
                // Level2 = power(2, L2.VMax) + power(2, L2.VMin)
                result = (int)Math.Pow(2, _threeOfKindKickerValue1) + (int)Math.Pow(2, _threeOfKindKickerValue2);
            }
            else if (TwoPairs)
            {
                // Level2 = hodnota nizsieho paru
                result = Math.Min(_twoPairsValue1, _twoPairsValue2);
            }
            else if (OnePair)
            {
                result = CardsBitmap;
            }

            return result;
        }

        private int? _level3;
        public int Level3 => _level3 ??= GetLevel3();

        private int GetLevel3()
        {
            var result = 0;

            if (RoyalStraightFlush) { }
            else if (StraightFlush) { }
            else if (FourOfKind) { }
            else if (FullHouse) { }
            else if (Flush) { }
            else if (Straight) { }
            else if (ThreeOfKind) { }
            else if (TwoPairs)
            {
                // Level3 = "kicker" = 5. karta, ktora nie je ani v jednom pare
                foreach (var card in _cards)
                {
                    var value = (byte)card.Rank;

                    if (value != _twoPairsValue1 && value != _twoPairsValue2)
                    {
                        result = value;
                        break;
                    }
                }
            }
            else if (OnePair) { }

            return result;
        }

        private int? _cardsBitmap;
        private int CardsBitmap => _cardsBitmap ??= GetCardsBitmap();

        private int GetCardsBitmap()
        {
            var result = 0;
            foreach (var card in _cards)
            {
                result += (int)Math.Pow(2, (int)card.Rank);
            }
            return result;
        }

        private int? _cardsBitmap2;
        private int CardsBitmap2 => _cardsBitmap2 ??= GetCardsBitmap2();

        private int GetCardsBitmap2()
        {
            var result = 0;
            foreach (var card in _cards)
            {
                var value = (int)card.Rank;
                if (card.Rank == CardCodeRank.Ace)
                {
                    value = 1;
                }

                result += (int)Math.Pow(2, value);
            }
            return result;
        }

        private int CompareTo(HandCardsBest cards)
        {
            var result = 0;

            if (Category > cards.Category)
            {
                result = 1;
            }
            else if (Category == cards.Category)
            {
                if (Level1 > cards.Level1)
                {
                    result = 1;
                }
                else if (Level1 == cards.Level1)
                {
                    if (Level2 > cards.Level2)
                    {
                        result = 1;
                    }
                    else if (Level2 == cards.Level2)
                    {
                        if (Level3 > cards.Level3)
                        {
                            result = 1;
                        }
                        else if (Level3 < cards.Level3)
                        {
                            result = -1;
                        }
                    }
                    else
                    {
                        result = -1;
                    }
                }
                else
                {
                    result = -1;
                }
            }
            else
            {
                result = -1;
            }

            return result;
        }
    }
}