<Project Sdk="Microsoft.NET.Sdk.Web">
    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
        <EmbeddedResourceUseDependentUponConvention>false</EmbeddedResourceUseDependentUponConvention>
        <SatelliteResourceLanguages>en;cs;sk</SatelliteResourceLanguages>
        <NoWarn>CA1416</NoWarn>
        <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
        <LangVersion>latest</LangVersion>
        <NeutralLanguage>cs</NeutralLanguage>
        <IsPackable>false</IsPackable>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <Import Condition="$(CommonBuildTargets) != ''" Project="$(CommonBuildTargets)\NetCoreWeb.targets" />

    <ItemGroup>
        <PackageReference Include="BuildWebCompiler2022" Version="1.14.15" />
        <PackageReference Include="EDF.Web.Components" Version="25.12.14" />
        <PackageReference Include="Microsoft.Web.LibraryManager.Build" Version="3.0.71" />
    </ItemGroup>

    <ItemGroup>
        <Content Update="compilerconfig.json" CopyToPublishDirectory="Never" />
        <Content Update="libman.json" CopyToPublishDirectory="Never" />
        <Content Include="..\..\..\.dockerignore" Link=".dockerignore" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Common\Poker.Common.Api.Public\Poker.Common.Api.Public.csproj" />
        <ProjectReference Include="..\..\Common\Poker.Common.Web.Authentication\Poker.Common.Web.Authentication.csproj" />
    </ItemGroup>
</Project>