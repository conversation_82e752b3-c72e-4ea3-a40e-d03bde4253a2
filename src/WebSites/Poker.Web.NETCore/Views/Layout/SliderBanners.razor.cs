using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Poker.Common.Web.Authentication;
using Poker.Web.NETCore.Models;
using Poker.Web.NETCore.Services;
using System;
using System.Threading.Tasks;

namespace Poker.Web.NETCore.Views.Layout
{
    public partial class SliderBanners : ComponentBase, IDisposable
    {
        private PersistingComponentStateSubscription _subscription;
        private ElementReference _element;
        private ContentSlidersViewModel _slider;

        [Inject] private ISettingsWebService SettingsWebService { get; set; }
        [Inject] private ICurrentUserService CurrentUserService { get; set; }
        [Inject] private IJSRuntime JSRuntime { get; set; }
        [Inject] private IContentManagementWebService ContentManagementWebService { get; set; }
        [Inject] private PersistentComponentState ApplicationState { get; set; }

        [Parameter] public bool Mobile { get; set; }

        private string PersistKey => $"{nameof(SliderBanners)}.{Mobile}";
        private bool Collapse => SettingsWebService.Settings.CollapseBanner;

        protected override void OnInitialized()
        {
            _subscription = ApplicationState.RegisterOnPersisting(() =>
            {
                ApplicationState.PersistAsJson(PersistKey, _slider);
                return Task.CompletedTask;
            });

            _slider = ApplicationState.TryTakeFromJson<ContentSlidersViewModel>(PersistKey, out var slider)
                ? slider
                : ContentManagementWebService.GetSliders(Mobile, CurrentUserService.User.IsAuthenticated);
        }

        public void Dispose()
        {
            _subscription.Dispose();
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender && _slider?.Items?.Length > 0)
            {
                await JSRuntime.InvokeVoidAsync("poker.sliderBannersCreate", _element, _slider.RotateSpeed);
            }
        }

        private async Task ToggleBanner()
        {
            await SettingsWebService.Update(x => x with { CollapseBanner = !x.CollapseBanner });
        }
    }
}