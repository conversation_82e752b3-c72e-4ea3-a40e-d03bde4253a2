<div class="link-panel__cover" data-test-role="@DataTestRole">
    <NavLink href="@Url" class="link-panel__box">
        <div class="link-panel__background"></div>
        <div class="link-panel__content">
            <div class="link-panel__headline">
                <div class="link-panel__chip @(Chip)"></div>
                @Localizer.FormatEnum(RoomType)
            </div>
            <div class="link-panel__description">
                @Description
            </div>
            <div class="link-panel__buttons-placeholder"></div>
        </div>
    </NavLink>
    <div class="link-panel__buttons">
        @if (Url is null)
        {
            <div class="btn disabled text-uppercase">@Localizer[nameof(SharedResource.ComingSoon)]</div>
        }
        else
        {
            <NavLink href="@Url" class="btn btn-primary">
                @Localizer[nameof(SharedResource.Play)]
            </NavLink>
            @ChildContent
        }
    </div>
</div>
