@page "/Stakes"
@using Poker.Common.Data.Interface.Models.Stakes
@attribute [Authorize]

<GameListLayout Data="_items"
                Class="stakes"
                ClassTable="stakes"
                TitleList="@Localizer[nameof(SharedResource.ListStake)]"
                TitleFilter="@Localizer[nameof(SharedResource.FilterStake)]"
                TitleCount="@Localizer[nameof(SharedResource.FoundStakes), _items.Metadata.TotalItemCount]">
    <Filters>
        <div class="game-filters__select">
            <span>@Localizer[nameof(SharedResource.Type)]</span>
            <InputSelect class="form-select" @bind-Value="_filter.RoomType" @bind-Value:after="UpdateFilter">
                <option value="@Common.Data.Interface.Enums.RoomType.Tournament">@Localizer.FormatEnum(Common.Data.Interface.Enums.RoomType.Tournament)</option>
                <option value="@Common.Data.Interface.Enums.RoomType.SitAndGo">@Localizer.FormatEnum(Common.Data.Interface.Enums.RoomType.SitAndGo)</option>
                <option value="@Common.Data.Interface.Enums.RoomType.CashGame">@Localizer.FormatEnum(Common.Data.Interface.Enums.RoomType.CashGame)</option>
                <option value="@Common.Data.Interface.Enums.RoomType.FastCashGame">@Localizer.FormatEnum(Common.Data.Interface.Enums.RoomType.FastCashGame)</option>
            </InputSelect>
        </div>
        <div class="game-filters__select">
            <span>@Localizer[nameof(SharedResource.DateTime)]</span>
            <FormDateRangePicker @bind-DateRange="DateRange" @bind-DateRange:after="UpdateFilter" />
        </div>
        <div class="game-filters__button">
            <button class="btn btn-secondary" @onclick="ClearFilter">@Localizer[nameof(SharedResource.CancelFilter)]</button>
        </div>
    </Filters>
    <Columns>
        <GameListHeaderColumn Filter="_filter" Text="@Localizer[nameof(SharedResource.StakeStart)]" Icon="icon-date" Column="StakeSort.Start" OnSort="UpdateFilter" />
        <GameListHeaderColumn Filter="_filter" Text="@Localizer[nameof(SharedResource.Deposit)]" Icon="icon-buy-in" Column="StakeSort.Stake" OnSort="UpdateFilter" />
        <GameListHeaderColumn Filter="_filter" Text="@Localizer[nameof(SharedResource.StakePrize)]" Icon="icon-prize-pool" Column="StakeSort.Prize" OnSort="UpdateFilter" />
        <GameListHeaderColumn Filter="_filter" />
    </Columns>
    <Row>
        <NavLink href="@($"Stakes/{context.RoomType}/{context.ID}")" @key="@($"{_filter.RoomType}_{context.ID}")" class="table__box">
            <div class="table__item table__item--started">
                <i class="stechicons icon-date"></i>@context.Start.ToDefaultLongString()</div>
            <div class="table__item table__item--stake">
                <i class="stechicons icon-minimum-bet"></i>@Localizer.FormatCurrency(context.Stake, true)</div>
            <div class="table__item table__item--win">
                <i class="stechicons icon-prize-pool"></i>@(context.Prize > 0 ? (context.Points ? Localizer.FormatPoints(context.Prize) : Localizer.FormatCurrency(context.Prize, true)) : " - ")
            </div>
            <div class="table__item table__item--button">
                <div class="btn btn-primary">@Localizer[nameof(SharedResource.StakeDetail)]</div>
            </div>
        </NavLink>
    </Row>
</GameListLayout>