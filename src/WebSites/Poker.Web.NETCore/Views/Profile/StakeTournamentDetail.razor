@page "/Stakes/Tournament/{ID:int}/{HandID:int?}"
@page "/Stakes/SitAndGo/{ID:int}/{HandID:int?}"
@layout Layout.LayoutMainMobile

@attribute [Authorize]

@if (_model is not null)
{
    <div class="detail-page detail-page--stakes detail-page--stakes-tournaments">
        <div class="detail-page__header">
            <div>
                <div class="detail-page__chip @($"list-layout--{RoomType.ToString().ToLower()}__chip")"></div>
            </div>
            @if (_model.PrizePoolMin is not null)
            {
                <div class="detail-page__prize">
                    <i class="stechicons icon-prize-pool"></i>
                    @if (_model.PayType == TournamentPayType.Free)
                    {
                        @Localizer.FormatPoints(_model.PrizePoolMin)
                    }
                    else
                    {
                        @Localizer.FormatCurrency(_model.PrizePoolMin, true)
                    }
                </div>
            }
            <div class="detail-page__header-name">
                @_model.Name
            </div>
        </div>

        <div class="detail-page__content">
            <div class="detail-page__heading">
                @Localizer[nameof(SharedResource.TournamentDetail)]
            </div>

            <div class="detail-page__box">
                <div class="detail-page__item">
                    <div class="detail-page__description"><i class="stechicons icon-info"></i> @Localizer[nameof(SharedResource.StakeTicketID)]:</div>
                    <div>@_model.StakeID</div>
                </div>
                <div class="detail-page__item">
                    <div class="detail-page__description"><i class="stechicons icon-finished"></i>@Localizer[nameof(SharedResource.TournamentStart)]:</div>
                    <div>@(_model.Started is null ? "-" : _model.Started.Value.ToDefaultLongWithSecondsString())</div>
                </div>
                <div class="detail-page__item">
                    <div class="detail-page__description"><i class="stechicons icon-finished"></i>@Localizer[nameof(SharedResource.TournamentFinish)]:</div>
                    <div>@(_model.Finish is null ? "-" : _model.Finish.Value.ToDefaultLongWithSecondsString())</div>
                </div>
                <div class="detail-page__item">
                    <div class="detail-page__description"><i class="stechicons icon-register"></i>@Localizer[nameof(SharedResource.TournamentStateType_RegistrationOpened)]:</div>
                    <div>@(_model.RegistrationClosed is null ? "-" : _model.RegistrationClosed.Value.ToDefaultLongString())</div>
                </div>
                <div class="detail-page__item">
                    <div class="detail-page__description"><i class="stechicons icon-coins"></i> @Localizer[nameof(SharedResource.Rebuy)]:</div>
                    <div>@(_model.RebuyEnd is null ? "-" : _model.RebuyEnd.Value.ToDefaultLongString())</div>
                </div>
                <div class="detail-page__item">
                    <div class="detail-page__description"><i class="stechicons icon-time"></i> @Localizer[nameof(SharedResource.TournamentLength)]:</div>
                    <div>@((_model.Finish - _model.Started)?.ToDefaultString() ?? "-")</div>
                </div>
            </div>

            <div class="detail-page__box">
                <div class="detail-page__item">
                    <div class="detail-page__description"><i class="stechicons icon-coins"></i> @Localizer[nameof(SharedResource.Deposit)]:</div>
                    <div>@_model.RegistrationCount x (@Localizer.FormatCurrency(_model.RegistrationPrice) + @Localizer.FormatCurrency(_model.RegistrationCommission)) @Localizer.CurrencySymbol</div>
                </div>
                <div class="detail-page__item">
                    <div class="detail-page__description"><i class="stechicons icon-coins"></i> @Localizer[nameof(SharedResource.Rebuy)]:</div>
                    <div>
                        @if (_model.Type == TournamentType.Rebuy && _model.RebuyCount > 0)
                        {
                            <span>
                                @_model.RebuyCount x (@Localizer.FormatCurrency(_model.RebuyPrice) + @Localizer.FormatCurrency(_model.RebuyCommission)) @Localizer.CurrencySymbol
                            </span>
                        }
                        else
                        {
                            <span>-</span>
                        }
                    </div>
                </div>
                <div class="detail-page__item">
                    <div class="detail-page__description"><i class="stechicons icon-dollar"></i> @Localizer[nameof(SharedResource.Addon)]:</div>
                    <div>
                        @if (_model.Type == TournamentType.Rebuy && _model.AddonCount > 0)
                        {
                            <span>

                                @_model.AddonCount x (@Localizer.FormatCurrency(_model.AddonPrice) + @Localizer.FormatCurrency(_model.AddonCommission)) @Localizer.CurrencySymbol
                            </span>
                        }
                        else
                        {
                            <span>-</span>
                        }
                    </div>
                </div>
                <div class="detail-page__item">
                    <div class="detail-page__description"><i class="stechicons icon-prize-pool"></i> @Localizer[nameof(SharedResource.TotalDeposit)]:</div>
                    <div>@Localizer.FormatCurrency(_model.Stake, true)</div>
                </div>
                <div class="detail-page__item">
                    <div class="detail-page__description"><i class="stechicons icon-cards"></i> @Localizer[nameof(SharedResource.Game)]:</div>
                    <div>Hold'em</div>
                </div>
                <div class="detail-page__item">
                    <div class="detail-page__description"><i class="stechicons icon-card-symbols"></i> @Localizer[nameof(SharedResource.Type)]:</div>
                    <div>@_model.Type</div>
                </div>
                <div class="detail-page__item">
                    <div class="detail-page__description"><i class="stechicons icon-win-rules"></i> @Localizer[nameof(SharedResource.QualificationTournament)]:</div>
                    <div>@Localizer.FormatBool(_model.IsQualification)</div>
                </div>
                <div class="detail-page__item">
                    <div class="detail-page__description"><i class="stechicons icon-info"></i> @Localizer[nameof(SharedResource.TournamentID)]:</div>
                    <div>
                        <NavLink href="@($"{(_model.Type == TournamentType.SitAndGo ? "SitAndGo" : "Tournaments")}/{_model.TournamentID}")">@_model.TournamentID</NavLink>
                    </div>
                </div>
            </div>
        </div>
        @if (_hand is not null)
        {
            <div class="detail-page__content">
                <div class="detail-page__heading">
                    <span>@Localizer[nameof(SharedResource.StakeHandID)]:</span>
                    <span class="detail-page__heading--prize">@_hand.HandID</span>
                </div>

                <div class="detail-page__box">
                    <div class="detail-page__timepicker">
                        <div class="detail-page__timepicker-heading">Datum a čas</div>
                        <NavLink href="@($"Stakes/{(_model.Type == TournamentType.SitAndGo ? "SitAndGo" : "Tournament")}/{_model.TournamentID}/{_hand.HandIDPrevious}")"
                                 class="@(_hand.HandIDPrevious == null ? "btn btn-secondary btn-sm disabled" : "btn btn-secondary btn-sm")">
                            &lt;
                        </NavLink>
                        <FormDateRangePicker
                            @bind-Date="_date"
                            @bind-Date:after="UpdateFilter"
                            SingleDatePicker="true"
                            TimePicker="true"
                            DateMin="_model.Started"
                            DateMax="_model.Finish" />
                        <NavLink href="@($"Stakes/{(_model.Type == TournamentType.SitAndGo ? "SitAndGo" : "Tournament")}/{_model.TournamentID}/{_hand.HandIDNext}")"
                                 class="@(_hand.HandIDNext is null ? "btn btn-secondary btn-sm disabled" : "btn btn-secondary btn-sm")">
                            &gt;
                        </NavLink>
                    </div>
                    <div class="detail-page__item">
                        <div class="detail-page__description"><i class="stechicons icon-cards"></i> @Localizer[nameof(SharedResource.CardsDealt)]:</div>
                        <div>
                            @foreach (var card in _hand.CardCodes)
                            {
                                <img class="detail-page__description-card" src="img/cards/@($"{card.GetRank()}_{card.GetSuit()}").png" />
                            }
                        </div>
                    </div>
                    <div class="detail-page__item">
                        <div class="detail-page__description"><i class="stechicons icon-date"></i> @Localizer[nameof(SharedResource.HandStart)]:</div>
                        <div>@_hand.Start.ToDefaultLongString()</div>
                    </div>
                </div>
                <div class="detail-page__box">
                    <div class="detail-page__item">
                        <div class="detail-page__description"><i class="stechicons icon-coins"></i> @Localizer[nameof(SharedResource.StakeAmount)]:</div>
                        <div>@Localizer.FormatCurrency(_hand.Stake)</div>
                    </div>
                    <div class="detail-page__item">
                        <div class="detail-page__description"><i class="stechicons icon-prize-pool"></i> @Localizer[nameof(SharedResource.StakePrize)]:</div>
                        <div>@Localizer.FormatCurrency(_hand.Prize)</div>
                    </div>
                    <div class="detail-page__item">
                        <div class="detail-page__description">
                            <i class="stechicons icon-finished"></i> @Localizer[nameof(SharedResource.HandEnd)]
                        </div>
                        <div>@_hand.Terminate.ToDefaultLongString()</div>
                    </div>
                    <div class="detail-page__item">
                        <div class="detail-page__description"><i class="stechicons icon-time"></i> @Localizer[nameof(SharedResource.HandDuration)]:</div>
                        <div>@_hand.Duration.ToDefaultString()</div>
                    </div>
                    <div class="detail-page__item">
                        <div class="detail-page__description"><i class="stechicons icon-info"></i> @Localizer[nameof(SharedResource.CashGameID)]:</div>
                        <div>@_hand.TableID</div>
                    </div>
                </div>

                @foreach (var transaction in _hand.Transactions)
                {
                    <StakeTransaction @key="@($"{transaction.ID}_{transaction.PlayerID}")" Model="transaction" />
                }
            </div>
        }
    </div>
}