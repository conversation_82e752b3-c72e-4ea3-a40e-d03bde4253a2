using Casino.Common.NETStandard;
using Microsoft.AspNetCore.Components;
using Poker.Common.Data.Interface.Enums;
using Poker.Common.Data.Interface.Models.Stakes;
using Poker.Web.NETCore.Helpers;
using Poker.Web.NETCore.Models;
using Poker.Web.NETCore.Services;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Poker.Web.NETCore.Views.Profile
{
    public partial class StakeList : ComponentBase, IDisposable
    {
        private readonly StakeViewFilter _filter = new();
        private PagedListViewModel<StakeViewModel> _items = new();
        private PersistingComponentStateSubscription _subscription;
        private bool _cached;

        [Inject] private ILocalizerService Localizer { get; set; }
        [Inject] private IStakeWebService StakeWebService { get; set; }
        [Inject] private IUrlQueryService UrlQueryService { get; set; }
        [Inject] private PersistentComponentState ApplicationState { get; set; }

        [SupplyParameterFromQuery] public DateTime? From { get; set; }
        [SupplyParameterFromQuery] public DateTime? To { get; set; }
        [SupplyParameterFromQuery] public string RoomType { get; set; }
        [SupplyParameterFromQuery] private string Column { get; set; }
        [SupplyParameterFromQuery] private string Direction { get; set; }
        [SupplyParameterFromQuery] private int? Page { get; set; }

        private StakeSort DefaultColumn => StakeSort.Start;
        private OrderingDirection DefaultDirection => OrderingDirection.Desc;

        private DateRangeViewModel DateRange
        {
            get => new(_filter.DateFrom, _filter.DateTo);
            set
            {
                _filter.DateFrom = value.From;
                _filter.DateTo = value.To;
            }
        }

        protected override async Task OnParametersSetAsync()
        {
            _filter.DateFrom = From;
            _filter.DateTo = To;
            _filter.RoomType = Enum.TryParse<RoomType>(RoomType, out var game) ? game : Common.Data.Interface.Enums.RoomType.Tournament;
            _filter.Page = Page ?? 1;

            _filter.OrderByColumn = Enum.TryParse<StakeSort>(Column, out var column) ? column : DefaultColumn;
            _filter.OrderByDirection = Enum.TryParse<OrderingDirection>(Direction, out var direction) ? direction : DefaultDirection;

            if (_cached)
            {
                _cached = false;
            }
            else
            {
                _items = await StakeWebService.ListAsync(_filter);
            }
        }

        protected override void OnInitialized()
        {
            _subscription = ApplicationState.RegisterOnPersisting(() =>
            {
                ApplicationState.PersistAsJson($"{nameof(StakeList)}.Items", _items.Items);
                ApplicationState.PersistAsJson($"{nameof(StakeList)}.Metadata", _items.Metadata);
                return Task.CompletedTask;
            });

            if (ApplicationState.TryTakeFromJson<IReadOnlyList<StakeViewModel>>($"{nameof(StakeList)}.Items", out var items)
                && ApplicationState.TryTakeFromJson<PagedListMetadataViewModel>($"{nameof(StakeList)}.Metadata", out var metadata))
            {
                _items = new PagedListViewModel<StakeViewModel>(items, metadata);
                _cached = true;
            }
        }

        public void Dispose()
        {
            _subscription.Dispose();
        }

        private void ClearFilter()
        {
            UrlQueryService.UpdateQuery(
                new UrlQueryParameter<string>(nameof(From)),
                new UrlQueryParameter<string>(nameof(To)),
                new UrlQueryParameter<RoomType>(nameof(RoomType)),
                new UrlQueryParameter<int?>(nameof(Page)),
                new UrlQueryParameter<StakeSort?>(nameof(Column)),
                new UrlQueryParameter<OrderingDirection?>(nameof(Direction))
            );
        }

        private void UpdateFilter()
        {
            if (_filter.DateFrom != From
                || _filter.DateTo != To
                || _filter.RoomType.ToString() != (RoomType ?? nameof(Common.Data.Interface.Enums.RoomType.Tournament)))
            {
                _filter.Page = 1;
            }

            UrlQueryService.UpdateQuery(
                new UrlQueryParameter<DateTime?>(nameof(From), _filter.DateFrom),
                new UrlQueryParameter<DateTime?>(nameof(To), _filter.DateTo),
                new UrlQueryParameter<RoomType>(nameof(RoomType), _filter.RoomType, Common.Data.Interface.Enums.RoomType.Tournament),
                new UrlQueryParameter<int?>(nameof(Page), _filter.Page, 1),
                new UrlQueryParameter<StakeSort?>(nameof(Column), _filter.OrderByColumn, DefaultColumn),
                new UrlQueryParameter<OrderingDirection?>(nameof(Direction), _filter.OrderByDirection, DefaultDirection)
            );
        }
    }
}