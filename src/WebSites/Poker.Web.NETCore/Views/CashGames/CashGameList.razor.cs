using Casino.Common.NETStandard;
using Microsoft.AspNetCore.Components;
using Poker.Common.Data.Interface.Enums;
using Poker.Common.Data.Interface.Models.CashGames;
using Poker.Web.NETCore.Helpers;
using Poker.Web.NETCore.Models;
using Poker.Web.NETCore.Services.CashGames;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Poker.Web.NETCore.Views.CashGames
{
    public partial class CashGameList : ComponentBase, IDisposable
    {
        private readonly CashGameViewFilter _filter = new();
        private PagedListViewModel<CashGameViewModel> _items = new();
        private StakeLimitViewModel _limits = new();
        private PersistingComponentStateSubscription _subscription;
        private bool _cached;

        [Inject] private ILocalizerService Localizer { get; set; }
        [Inject] private IUrlQueryService UrlQueryService { get; set; }
        [Inject] private ICashGamesWebService CashGamesWebService { get; set; }
        [Inject] private PersistentComponentState ApplicationState { get; set; }

        [SupplyParameterFromQuery] public bool ForFun { get; set; }
        [SupplyParameterFromQuery] public int? PlayersCountFrom { get; set; }
        [SupplyParameterFromQuery] public int? PlayersCountTo { get; set; }
        [SupplyParameterFromQuery] public decimal? StakeFrom { get; set; }
        [SupplyParameterFromQuery] public decimal? StakeTo { get; set; }
        [SupplyParameterFromQuery] private string Column { get; set; }
        [SupplyParameterFromQuery] private string Direction { get; set; }
        [SupplyParameterFromQuery] private int? Page { get; set; }

        private CashGameSort DefaultColumn => CashGameSort.BigBlind;
        private OrderingDirection DefaultDirection => OrderingDirection.Asc;

        protected override async Task OnParametersSetAsync()
        {
            if (_limits.Min is null || _limits.Max is null)
            {
                _limits = await CashGamesWebService.GetLimitsAsync(ForFun ? [RoomType.ForFun] : [RoomType.CashGame, RoomType.FastCashGame]);
            }

            _filter.ForFun = ForFun;
            _filter.RoomTypes = ForFun ? [RoomType.ForFun] : [RoomType.CashGame, RoomType.FastCashGame];
            _filter.PlayersCountFrom = PlayersCountFrom ?? 0;
            _filter.PlayersCountTo = PlayersCountTo ?? 9;
            _filter.StakeFrom = StakeFrom ?? _limits.Min * 100;
            _filter.StakeTo = StakeTo ?? _limits.Max * 100;
            _filter.Page = Page ?? 1;
            _filter.PageSize = null;

            if (_filter.StakeFrom < _limits.Min * 100)
            {
                _filter.StakeFrom = _limits.Min * 100;
            }
            if (_filter.StakeTo > _limits.Max * 100)
            {
                _filter.StakeTo = _limits.Max * 100;
            }

            _filter.OrderByColumn = Enum.TryParse<CashGameSort>(Column, out var column) ? column : DefaultColumn;
            _filter.OrderByDirection = Enum.TryParse<OrderingDirection>(Direction, out var direction) ? direction : DefaultDirection;

            if (_cached)
            {
                _cached = false;
            }
            else
            {
                _items = await CashGamesWebService.ListAsync(_filter);
            }
        }

        protected override void OnInitialized()
        {
            _subscription = ApplicationState.RegisterOnPersisting(() =>
            {
                ApplicationState.PersistAsJson($"{nameof(CashGameList)}.Items", _items.Items);
                ApplicationState.PersistAsJson($"{nameof(CashGameList)}.Metadata", _items.Metadata);
                ApplicationState.PersistAsJson($"{nameof(CashGameList)}.Limits", _limits);
                return Task.CompletedTask;
            });

            if (ApplicationState.TryTakeFromJson<IReadOnlyList<CashGameViewModel>>($"{nameof(CashGameList)}.Items", out var items)
                && ApplicationState.TryTakeFromJson<PagedListMetadataViewModel>($"{nameof(CashGameList)}.Metadata", out var metadata)
                && ApplicationState.TryTakeFromJson<StakeLimitViewModel>($"{nameof(CashGameList)}.Limits", out var limits))
            {
                _items = new PagedListViewModel<CashGameViewModel>(items, metadata);
                _limits = limits;
                _cached = true;
            }
        }

        public void Dispose()
        {
            _subscription.Dispose();
        }

        private void ClearFilter()
        {
            UrlQueryService.UpdateQuery(
                new UrlQueryParameter<bool>(nameof(ForFun), _filter.ForFun),
                new UrlQueryParameter<int?>(nameof(PlayersCountFrom)),
                new UrlQueryParameter<int?>(nameof(PlayersCountTo)),
                new UrlQueryParameter<decimal?>(nameof(StakeFrom)),
                new UrlQueryParameter<decimal?>(nameof(StakeTo)),
                new UrlQueryParameter<int?>(nameof(Page)),
                new UrlQueryParameter<CashGameSort?>(nameof(Column)),
                new UrlQueryParameter<OrderingDirection?>(nameof(Direction))
            );
        }

        private void UpdateFilter()
        {
            if (_filter.ForFun != ForFun)
            {
                _limits = new();
                _filter.StakeFrom = null;
                _filter.StakeTo = null;
            }

            if (_filter.PlayersCountFrom != (PlayersCountFrom ?? 0)
                || _filter.PlayersCountTo != (PlayersCountTo ?? 9)
                || _filter.StakeFrom != (StakeFrom ?? _limits.Min)
                || _filter.StakeTo != (StakeTo ?? _limits.Max))
            {
                _filter.Page = 1;
            }

            UrlQueryService.UpdateQuery(
                new UrlQueryParameter<bool>(nameof(ForFun), _filter.ForFun),
                new UrlQueryParameter<int?>(nameof(PlayersCountFrom), _filter.PlayersCountFrom, 0),
                new UrlQueryParameter<int?>(nameof(PlayersCountTo), _filter.PlayersCountTo, 9),
                new UrlQueryParameter<decimal?>(nameof(StakeFrom), _filter.StakeFrom, _limits.Min * 100),
                new UrlQueryParameter<decimal?>(nameof(StakeTo), _filter.StakeTo, _limits.Max * 100),
                new UrlQueryParameter<int?>(nameof(Page), _filter.Page, 1),
                new UrlQueryParameter<CashGameSort?>(nameof(Column), _filter.OrderByColumn, DefaultColumn),
                new UrlQueryParameter<OrderingDirection?>(nameof(Direction), _filter.OrderByDirection, DefaultDirection)
            );
        }
    }
}