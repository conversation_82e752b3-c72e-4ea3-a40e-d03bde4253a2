using Casino.Common.NETStandard;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Poker.Common.Data.Interface.Models.Stakes;
using Poker.Common.Data.Interface.Repositories;
using Poker.Common.Infrastructure.Logging;
using Poker.Common.Infrastructure.Performance;
using Poker.Common.Web.Authentication;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Poker.Web.NETCore.Controllers
{
    [ApiController, Authorize, Route("ApiPublic/Stakes/[action]"), Tags("Stakes")]
    public class StakeController : ControllerBase
    {
        private readonly ILogger<TournamentWindowsController> _log;
        private readonly IStakeRepository _stakeRepository;
        private readonly ICurrentUserServiceSetter _currentUserService;
        private readonly IPerformanceCounterService _performanceCounterService;

        public StakeController(
            ILogger<TournamentWindowsController> log,
            IStakeRepository stakeRepository,
            ICurrentUserServiceSetter currentUserService,
            IPerformanceCounterService performanceCounterService)
        {
            _log = log;
            _stakeRepository = stakeRepository;
            _currentUserService = currentUserService;
            _performanceCounterService = performanceCounterService;
        }

        [HttpPost]
        public async Task<StakePublicDto[]> ListAsync(StakePublicRequest request, CancellationToken cancellationToken = default)
        {
            using (_performanceCounterService.Measure(PerformanceCategory.ApiPublic, "StakeList"))
            {
                await _currentUserService.InitializeAsync(HttpContext);
                var user = _currentUserService.User;
                using (_log.UsePlayer(user.ID))
                {
                    var stakes = await _stakeRepository.ListAsync(new StakeFilter
                    {
                        PlayerID = user.ID,
                        DateFrom = request.DateFrom,
                        DateTo = request.DateTo,
                        RoomTypes = request.RoomTypes,
                        Page = request.Page,
                        PageSize = request.PageSize,
                        OrderByColumn = StakeSort.Start,
                        OrderByDirection = OrderingDirection.Desc
                    }, cancellationToken);

                    return stakes.Select(x => new StakePublicDto
                    {
                        RoomType = x.RoomType,
                        ID = x.ID,
                        Created = x.Created,
                        Stake = x.Stake,
                        Prize = x.Prize,
                        Points = x.Points
                    }).ToArray();
                }
            }
        }
    }
}