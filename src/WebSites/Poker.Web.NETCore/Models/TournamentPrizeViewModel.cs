namespace Poker.Web.NETCore.Models
{
    public class TournamentPrizeViewModel
    {
        public required int Sequence { get; init; }
        public required string Winner { get; init; }

        public required decimal? Prize { get; init; }
        public required decimal? PrizePercent { get; init; }
        public required decimal? PrizePoints { get; init; }
        public required decimal? PrizeVoucher { get; init; }
        public required decimal? PrizeSatellite { get; init; }
        public required string PrizeName { get; init; }
    }
}