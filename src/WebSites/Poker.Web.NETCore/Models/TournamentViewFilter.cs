using Casino.Common.NETStandard;
using Poker.Common.Data.Interface.Enums;
using Poker.Common.Data.Interface.Models.Tournaments;
using Poker.Web.NETCore.Enums;

namespace Poker.Web.NETCore.Models
{
    public class TournamentViewFilter : Filter<TournamentSort>
    {
        public TournamentType? Type { get; set; }
        public TournamentType? TypeExclude { get; init; }
        public TournamentStateViewType State { get; set; }

        public int? PlayersCountFrom { get; set; }
        public int? PlayersCountTo { get; set; }

        public decimal? StakeFrom { get; set; }
        public decimal? StakeTo { get; set; }

        public bool Satellite { get; set; } = true;
        public bool Qualification { get; set; } = true;
    }
}