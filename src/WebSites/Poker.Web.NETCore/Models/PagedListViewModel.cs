using System;
using System.Collections.Generic;
using X.PagedList;

namespace Poker.Web.NETCore.Models
{
    public class PagedListViewModel<T>
    {
        public PagedListViewModel() { }

        public PagedListViewModel(IReadOnlyList<T> items, PagedListMetadataViewModel metadata)
        {
            Items = items;
            Metadata = metadata;
        }

        public PagedListViewModel(IReadOnlyList<T> items, IPagedList pagedList)
        {
            Items = items;
            Metadata = new PagedListMetadataViewModel
            {
                PageCount = pagedList.PageCount,
                TotalItemCount = pagedList.TotalItemCount,
                PageNumber = pagedList.PageNumber
            };
        }

        public IReadOnlyList<T> Items { get; } = Array.Empty<T>();
        public PagedListMetadataViewModel Metadata { get; } = new();
    }
}