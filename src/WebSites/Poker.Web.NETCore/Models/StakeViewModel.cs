using Poker.Common.Data.Interface.Enums;
using Poker.Web.NETCore.Enums;
using System;

namespace Poker.Web.NETCore.Models
{
    public class StakeViewModel
    {
        public required long ID { get; init; }
        public required RoomType RoomType { get; init; }

        public required DateTime Start { get; init; }
        public required decimal Stake { get; init; }
        public required decimal? Prize { get; init; }
        public required bool Points { get; init; }
    }
}