using Casino.Common.NETStandard;
using Poker.Common.Data.Interface.Enums;
using Poker.Common.Data.Interface.Models.CashGames;

namespace Poker.Web.NETCore.Models
{
    public class CashGameViewFilter : Filter<CashGameSort>
    {
        public bool ForFun { get; set; }
        public int? PlayersCountFrom { get; set; }
        public int? PlayersCountTo { get; set; }

        public decimal? StakeFrom { get; set; }
        public decimal? StakeTo { get; set; }
        public RoomType[] RoomTypes { get; set; }
    }
}