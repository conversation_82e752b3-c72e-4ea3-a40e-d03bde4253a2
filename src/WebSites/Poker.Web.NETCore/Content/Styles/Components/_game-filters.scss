.game-filters {
    margin: 0 0 $spacer*2 0;

    @include media-breakpoint-up(md) {
        margin: 37px 0 $spacer*2 0;
    }

    &__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
        font-size: 12px;
        text-transform: uppercase;

        @include media-breakpoint-up(md) {
            display: none;
        }
    }

    &__caret {
        margin-right: 3px;
        transition: all .3s;
        position: relative;
        top: -1px;
        transform: rotate(180deg);

        &.active {
            transform: rotate(-0deg);
        }
    }

    &__toggle {
        font-size: 20px;
        opacity: 1;
        transition: opacity .3s;

        &.active {
            opacity: .5;
        }
    }

    &__content {
        display: none;

        &.active {
            display: block;
        }

        @include media-breakpoint-up(md) {
            display: flex !important;
            align-items: center;
            justify-content: flex-start;
        }
    }

    &__range-slider {
        width: 100%;
        height: 70px;
        margin-bottom: 14px;

        @include media-breakpoint-up(md) {
            flex-basis: 22%;
            height: 0;
            position: relative;
        }
    }

    &__slider {
        @include media-breakpoint-up(md) {
            flex-direction: row;
            display: flex;
            white-space: nowrap;
            padding: 0 20px;

            @include media-breakpoint-up(md) {
                padding: 0 40px 0 0;
            }
        }
    }

    &__track {
        position: relative;
        left: 9.5%;
        width: 78%;

        @include media-breakpoint-up(md) {
            left: 0;
        }
    }

    &__title {
        text-align: center;
        font-size: 12px;
        margin-bottom: 39px;

        @include media-breakpoint-up(md) {
            margin-right: 20px;
        }
    }

    &__select {
        text-align: center;
        font-size: 12px;

        @include media-breakpoint-up(md) {
            flex-basis: 27%;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;

            .list-layout--stakes & {
                justify-content: start;
            }

            .list-layout--voucher & {
                justify-content: start;

                @include media-breakpoint-up(md) {
                    justify-content: center;
                }
            }
        }

        @include media-breakpoint-up(xxl) {
            flex-basis: 23%;
        }

        @media screen and (min-width: 1790px) {
            flex-basis: 17%;
        }

        span {
            display: inline-block;
            margin-bottom: 6px;
            white-space: nowrap;

            @include media-breakpoint-up(md) {
                margin-bottom: 0;
            }
        }

        select {
            cursor: pointer;
            border-radius: $btn-border-radius;
            margin-bottom: $spacer*2;
            height: 37px;

            @include media-breakpoint-up(md) {
                margin-bottom: 0;
                width: 200px;
                margin-left: 12px;
            }

            @include media-breakpoint-up(xxl) {
                width: 240px;
            }

            .list-layout & {
                @include media-breakpoint-up(md) {
                    width: 170px;
                }
            }
        }

        input {
            border-radius: $btn-border-radius;
            margin-bottom: $spacer*2;
            width: 100%;
            height: 37px;
            border: $input-border-width solid $input-border-color;
            background: $input-bg;
            color: $body-color;
            padding: 0.6rem 2.35rem 0.6rem 0.9rem;

            @include media-breakpoint-up(md) {
                margin-bottom: 0;
                width: 200px;
                margin-left: 12px;
            }

            @include media-breakpoint-up(xxl) {
                width: 240px;
            }

            .list-layout & {
                @include media-breakpoint-up(md) {
                    width: 170px;
                }
            }

            &:focus-visible {
                outline-width: 0;
            }

            &:focus {
                border-color: $input-focus-border-color;
                outline: 0;
            }
        }
    }

    &__buttons {
        width: 100%;
        display: flex;
        justify-content: space-between;

        @include media-breakpoint-up(md) {
            flex-basis: 20%;
        }

        .list-layout--cashgame & {
            @include media-breakpoint-up(md) {
                flex-basis: 48%;
                justify-content: space-between;
                margin-left: 28px;
                flex-grow: 1;
            }
        }
    }

    &__button {
        display: flex;
        justify-content: end;


        @include media-breakpoint-up(md) {
            flex-basis: 10%;
            flex-grow: 1;
        }
    }

    &__mode-toggle {
        display: flex;
        align-items: center;
        font-size: 12px;
        text-transform: uppercase;
        cursor: pointer;

        .list-layout--cashgame & {
            @include media-breakpoint-up(md) {
                margin-right: 20px;
            }
        }

        .form-switch {
            padding: 0;
            margin: 0 7px;

            .form-check-input {
                background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.90%29'/%3e%3c/svg%3e");
                width: 30px;
                height: 20px;
                cursor: pointer;

                &:focus {
                    box-shadow: none;
                    border-color: transparent;
                }

                &:checked {
                    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.90%29'/%3e%3c/svg%3e");
                    background-color: white;
                    border-color: white;
                }
            }

            input {
                margin: 0;
            }
        }

        label:not(.active) {
            opacity: .4;
        }
    }

    &__track {
        .noUi-target {
            border-radius: 0;
            top: 0;
            height: 3px;
            border: none;
            background: transparent;
            box-shadow: none;
        }

        .noUi-base {
            background: transparent;
        }

        .noUi-connects {
            background: #545454;
            position: relative;
            top: 4px;
        }

        .noUi-connect {
            background: #929292;
        }

        .noUi-handle {
            height: 23px !important;
            width: 23px !important;
            background: $primary;
            box-shadow: none;
            border: none;
            border-radius: 50%;
            cursor: pointer;

            &:after {
                display: none;
            }

            &:before {
                display: none;
            }
        }

        .noUi-tooltip {
            background: $input-bg;
            color: $body-color;
            border-color: $input-border-color;
            border-width: $input-border-width;
            border-radius: $btn-border-radius;
            padding: 2px 12px;
        }
    }
}
