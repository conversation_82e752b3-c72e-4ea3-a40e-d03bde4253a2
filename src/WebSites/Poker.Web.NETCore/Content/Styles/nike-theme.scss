@import "nike-variables";
@import "../Libs/bootstrap/scss/bootstrap";
@import "common";
@import "Components/components.scss";

$nike-green: #1b790a;

.text-primary {
    color: #cdb628 !important;

    &.timer {
        color: $primary !important;
    }
}

.btn.btn-primary {
    color: white;
    background: $nike-green;
    border-color: $nike-green;

    &:hover {
        background: darken($nike-green, 3%);
        border-color: darken($nike-green, 3%);
    }
}

.btn.btn-secondary {
    border-color: #494949;
}

.btn.btn-success {
    color: white;
    border-color: #1b790a;
    background: #1b790a;
}

.hover-buttons {
    &__buttons {
        button, a {
            @include media-breakpoint-up(md) {
                min-width: 110px;
            }
        }
    }
}

.link-panel__cover, .promotion__box, .game-filters__button, .game-filters__buttons {
    .btn-secondary {
        background: none;
        color: #808080;
        border-color: #808080;

        &:hover {
            color: white;
        }
    }
}

.table__headline-item.active {
    background: $secondary;
}

.menu-category {
    margin-top: $spacer*2;

    .menu-category__item.active:after {
        background: $primary;
    }
}

.detail-page__table-menu__item.active {
    color: $primary;
}

.table__box.active {
    background: #d0d0d0 !important;
}

.column-wrapper {
    &__logo {
        width: 52px;
        padding: 7px;
        aspect-ratio: 1;
        object-fit: contain;
        background: linear-gradient(180deg, #ef861e, #eb6917);
        border-radius: $btn-border-radius;
        overflow: hidden;
        flex-shrink: 0;
    }
}

.pagination {
    .page-item {
        .page-link {
            color: $body-color;
        }

        &:first-child .page-link:not(.disabled),
        &:last-child .page-link:not(.disabled) {
            color: $primary;
        }
    }
}

.list-layout--tournament__chip,
.link-panel__chip--tournaments,
.detail-page--tournament .detail-page__chip,
.closest-tournaments__chip {
    background-image: url(../img/chips/nike/chip_tournaments.png);
}

.list-layout--sitandgo__chip,
.link-panel__chip--sit-and-go,
.detail-page--sit-and-go .detail-page__chip {
    background-image: url(../img/chips/nike/chip_sit_and_go.png);
}

.list-layout--cashgame__chip,
.link-panel__chip--cash-games,
.promotion__img--action {
    background-image: url(../img/chips/nike/chip_cash_games.png);
}

.list-layout--fastcashgame__chip,
.link-panel__chip--fast-cash-games {
    background-image: url(../img/chips/nike/chip_fast_cash_games.png);
}

.box-highlight__cover--tournament,
.detail-page--tournament .detail-page__background {
    background-image: url(../img/components/nike/box-highlight__tournament.jpg);
}

.box-highlight__cover--sitandgo,
.detail-page--sit-and-go .detail-page__background {
    background-image: url(../img/components/nike/box-highlight__sitandgo.jpg);
}

.box-highlight__cover--cashgame {
    background-image: url(../img/components/nike/box-highlight__cashgame.jpg);
}

.box-highlight__cover--fastcashgame {
    background-image: url(../img/components/nike/box-highlight__fastcashgame.jpg);
}

.menu-category {
    .icon-tournaments:before,
    .icon-sit-and-go:before,
    .icon-cashgame:before,
    .icon-fast-cashgame:before {
        background-size: contain;
        background-repeat: no-repeat;
        content: " ";
        display: block;
        width: 24px;
        height: 24px;
    }

    .icon-tournaments:before {
        background-image: url(../img/layout/nike/menu_tournaments.svg);
    }

    .icon-sit-and-go:before {
        background-image: url(../img/layout/nike/menu_sit_and_go.svg);
    }

    .icon-cashgame:before {
        background-image: url(../img/layout/nike/menu_cashgames.svg);
    }

    .icon-fast-cashgame:before {
        background-image: url(../img/layout/nike/menu_fast_cash_games.svg);
    }
}
