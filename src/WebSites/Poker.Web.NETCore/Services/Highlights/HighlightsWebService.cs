using Poker.Common.Data.Interface.Enums;
using Poker.Common.Infrastructure.Performance;
using Poker.Common.Web.Authentication;
using Poker.Web.NETCore.Helpers;
using Poker.Web.NETCore.Models;
using System;
using System.Linq;

namespace Poker.Web.NETCore.Services.Highlights
{
    public class HighlightsWebService : IHighlightsWebService
    {
        private const int _highlightCount = 6;
        private readonly IHighlightsCashGamesService _highlightsCashGamesService;
        private readonly IHighlightsTournamentsService _highlightsTournamentsService;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILocalizerService _localizerService;
        private readonly IPerformanceCounterService _performanceCounterService;

        public HighlightsWebService(
            IHighlightsCashGamesService highlightsCashGamesService,
            IHighlightsTournamentsService highlightsTournamentsService,
            ICurrentUserService currentUserService,
            ILocalizerService localizerService,
            IPerformanceCounterService performanceCounterService
        )
        {
            _highlightsCashGamesService = highlightsCashGamesService;
            _highlightsTournamentsService = highlightsTournamentsService;
            _currentUserService = currentUserService;
            _localizerService = localizerService;
            _performanceCounterService = performanceCounterService;
        }

        public EventSubscription Subscribe(Action callback)
        {
            var cashGames = _highlightsCashGamesService.Subscribe(callback);
            var tournaments = _highlightsTournamentsService.Subscribe(callback);
            return new EventSubscription(() =>
            {
                cashGames.Dispose();
                tournaments.Dispose();
            });
        }

        public HighlightViewModel[] GetHighlights()
        {
            using (_performanceCounterService.Measure(PerformanceCategory.WebServiceHighlights, "GetHighlights"))
            {
                var fastCashGames = _highlightsCashGamesService.GetFastCashGameHighlights(_currentUserService.User.ID).Take(_highlightCount).ToArray();
                var cashGames = _highlightsCashGamesService.GetCashGameHighlights(_currentUserService.User.ID).Take(_highlightCount).ToArray();
                var tournaments = _highlightsTournamentsService.GetHighlightsTournaments(_currentUserService.User.ID).Take(_highlightCount).ToArray();
                var sitAndGoes = _highlightsTournamentsService.GetHighlightsSitAndGoes(_currentUserService.User.ID).Take(_highlightCount).ToArray();

                var index = 0;
                var highlights = new HighlightViewModel[fastCashGames.Length + cashGames.Length + tournaments.Length + sitAndGoes.Length];
                for (var i = 0; i < _highlightCount; i++)
                {
                    if (fastCashGames.Length > i)
                    {
                        highlights[index++] = new HighlightViewModel
                        {
                            RoomType = RoomType.FastCashGame,
                            ID = fastCashGames[i].ID,
                            Name = fastCashGames[i].Name,
                            Stake = $"{_localizerService.FormatCurrency(fastCashGames[i].SmallBlind)}/{_localizerService.FormatCurrency(fastCashGames[i].BigBlind, true)}",
                            Footer = $"{fastCashGames[i].PlayersCount}",
                            CashGame = fastCashGames[i]
                        };
                    }

                    if (cashGames.Length > i)
                    {
                        highlights[index++] = new HighlightViewModel
                        {
                            RoomType = RoomType.CashGame,
                            ID = cashGames[i].ID,
                            Name = cashGames[i].Name,
                            Stake = $"{_localizerService.FormatCurrency(cashGames[i].SmallBlind)}/{_localizerService.FormatCurrency(cashGames[i].BigBlind, true)}",
                            Footer = $"{cashGames[i].PlayersCount}/{cashGames[i].PlayersMax}",
                            CashGame = cashGames[i]
                        };
                    }

                    if (tournaments.Length > i)
                    {
                        highlights[index++] = new HighlightViewModel
                        {
                            RoomType = RoomType.Tournament,
                            ID = tournaments[i].ID,
                            Name = tournaments[i].Name,
                            Stake = $"{_localizerService.FormatCurrency(tournaments[i].BuyIn)} + {_localizerService.FormatCurrency(tournaments[i].Commission, true)}",
                            Footer = tournaments[i].PayType == TournamentPayType.Free
                                ? _localizerService.FormatPoints(tournaments[i].PrizePoolMin)
                                : _localizerService.FormatCurrency(tournaments[i].PrizePoolMin, true),
                            Tournament = tournaments[i]
                        };
                    }

                    if (sitAndGoes.Length > i)
                    {
                        highlights[index++] = new HighlightViewModel
                        {
                            RoomType = RoomType.SitAndGo,
                            ID = sitAndGoes[i].ID,
                            Name = sitAndGoes[i].Name,
                            Stake = $"{_localizerService.FormatCurrency(sitAndGoes[i].BuyIn)} + {_localizerService.FormatCurrency(sitAndGoes[i].Commission, true)}",
                            Footer = $"{sitAndGoes[i].PlayersCount}/{sitAndGoes[i].PlayersMax}",
                            Tournament = sitAndGoes[i]
                        };
                    }
                }
                return highlights;
            }
        }
    }
}