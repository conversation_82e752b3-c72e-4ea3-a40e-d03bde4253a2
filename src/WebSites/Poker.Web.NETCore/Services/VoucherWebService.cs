using Poker.Common.Data.Interface.Models.Vouchers;
using Poker.Common.Data.Interface.Repositories;
using Poker.Common.Infrastructure.Performance;
using Poker.Common.Infrastructure.Settings;
using Poker.Common.Logic.Interface.Services;
using Poker.Common.Web.Authentication;
using Poker.Web.NETCore.Enums;
using Poker.Web.NETCore.Models;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using X.PagedList;

namespace Poker.Web.NETCore.Services
{
    public class VoucherWebService : IVoucherWebService
    {
        private readonly ICurrentUserService _currentUserService;
        private readonly IVoucherPurchaseRepository _voucherPurchaseRepository;
        private readonly IVoucherPurchaseService _voucherPurchaseService;
        private readonly ISettings _settings;
        private readonly IPerformanceCounterService _performanceCounterService;

        public VoucherWebService(
            ICurrentUserService currentUserService,
            IVoucherPurchaseRepository voucherPurchaseRepository,
            IVoucherPurchaseService voucherPurchaseService,
            ISettings settings,
            IPerformanceCounterService performanceCounterService
        )
        {
            _currentUserService = currentUserService;
            _voucherPurchaseRepository = voucherPurchaseRepository;
            _voucherPurchaseService = voucherPurchaseService;
            _settings = settings;
            _performanceCounterService = performanceCounterService;
        }

        public async Task<PagedListViewModel<VoucherViewModel>> ListAsync(VoucherViewFilter filter, CancellationToken cancellationToken = default)
        {
            using (_performanceCounterService.Measure(PerformanceCategory.WebServiceVoucher, "List"))
            {
                bool? expired = filter.State switch
                {
                    VoucherState.Expired => true,
                    VoucherState.Active => false,
                    _ => null
                };

                var vouchers = await _voucherPurchaseRepository.ListAsync(new()
                {
                    PlayerID = _currentUserService.User.ID,
                    DateFrom = filter.DateFrom,
                    DateTo = filter.DateTo,
                    Expired = expired,
                    OrderByColumn = filter.OrderByColumn,
                    OrderByDirection = filter.OrderByDirection,
                    PageSize = filter.PageSize ?? _settings.Web.PageSize,
                    Page = filter.Page
                }, cancellationToken, x => x.VoucherTemplate, x => x.VoucherTemplate.VoucherProvider);

                var result = vouchers.Select(MapItem).ToArray();

                return new PagedListViewModel<VoucherViewModel>(result, vouchers);
            }
        }

        public async Task RefundAsync(VoucherViewModel voucher, CancellationToken cancellationToken = default)
        {
            await _voucherPurchaseService.RefundAsync(voucher.TournamentID, _currentUserService.User.ID, cancellationToken);
        }

        private static VoucherViewModel MapItem(VoucherPurchaseDto voucher)
        {
            return new VoucherViewModel
            {
                ID = voucher.ID,
                Name = voucher.VoucherTemplate.Name,
                VoucherProviderName = voucher.VoucherTemplate.VoucherProvider.Name,
                Date = voucher.VoucherTemplate.Date,
                Refunded = voucher.Refunded,
                RefundableTo = voucher.RefundableTo,
                Value = voucher.VoucherTemplate.Value,
                State = GetState(voucher),
                TournamentID = voucher.TournamentID
            };
        }

        private static VoucherState GetState(VoucherPurchaseDto voucher)
        {
            if (voucher.Refunded != null)
            {
                return VoucherState.Refunded;
            }
            if (voucher.VoucherTemplate.Date < DateTime.Now)
            {
                return VoucherState.Expired;
            }
            if (voucher.RefundableTo < DateTime.Now)
            {
                return VoucherState.Nonrefundable;
            }
            return VoucherState.Active;
        }
    }
}