using Poker.Common.Data.Interface.Enums;
using Poker.Common.Data.Interface.Models.Hands;
using Poker.Common.Data.Interface.Models.Stakes;
using Poker.Common.Data.Interface.Models.TournamentPrizes;
using Poker.Common.Data.Interface.Repositories;
using Poker.Common.Infrastructure.Performance;
using Poker.Common.Infrastructure.Settings;
using Poker.Common.Logic.Interface.Tournaments;
using Poker.Common.Web.Authentication;
using Poker.Web.NETCore.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using X.PagedList;

namespace Poker.Web.NETCore.Services
{
    public class StakeWebService : IStakeWebService
    {
        private readonly ICurrentUserService _currentUserService;
        private readonly ISettings _settings;
        private readonly IHandReportRepository _handReportRepository;
        private readonly IStakeRepository _stakeRepository;
        private readonly ITournamentPrizeService _tournamentPrizeService;
        private readonly IPerformanceCounterService _performanceCounterService;

        public StakeWebService(
            ICurrentUserService currentUserService,
            ISettings settings,
            IHandReportRepository handReportRepository,
            IStakeRepository stakeRepository,
            ITournamentPrizeService tournamentPrizeService,
            IPerformanceCounterService performanceCounterService
        )
        {
            _currentUserService = currentUserService;
            _settings = settings;
            _handReportRepository = handReportRepository;
            _stakeRepository = stakeRepository;
            _tournamentPrizeService = tournamentPrizeService;
            _performanceCounterService = performanceCounterService;
        }

        public async Task<PagedListViewModel<StakeViewModel>> ListAsync(StakeViewFilter filter, CancellationToken cancellationToken = default)
        {
            using (_performanceCounterService.Measure(PerformanceCategory.WebServiceStake, "ListAsync"))
            {
                var stakes = await _stakeRepository.ListAsync(new StakeFilter
                {
                    PlayerID = _currentUserService.User.ID,
                    DateFrom = filter.DateFrom,
                    DateTo = filter.DateTo,
                    RoomTypes = [filter.RoomType],
                    OrderByColumn = filter.OrderByColumn,
                    OrderByDirection = filter.OrderByDirection,
                    PageSize = filter.PageSize ?? _settings.Web.PageSize,
                    Page = filter.Page,
                }, cancellationToken);

                var result = stakes.Select(x => new StakeViewModel
                {
                    RoomType = x.RoomType,
                    ID = x.ID,
                    Start = x.Created,
                    Stake = x.Stake,
                    Prize = x.Prize,
                    Points = x.Points
                }).ToArray();

                return new PagedListViewModel<StakeViewModel>(result, stakes);
            }
        }

        public async Task<StakeCashGameDetailViewModel> GetCashGameDetailAsync(int handID, CancellationToken cancellationToken = default)
        {
            using (_performanceCounterService.Measure(PerformanceCategory.WebServiceStake, "GetCashGameDetail"))
            {
                var stake = await _stakeRepository.GetCashGameAsync(handID, _currentUserService.User.ID, cancellationToken);
                if (stake is null)
                {
                    return null;
                }

                var handDetail = await _handReportRepository.GetAsync(stake.HandID, cancellationToken);
                if (handDetail is null)
                {
                    return null;
                }

                var transactions = GetHandTransactions(handDetail);

                return new StakeCashGameDetailViewModel
                {
                    TicketID = stake.TicketID,
                    HandID = stake.HandID,
                    RoomType = stake.RoomType,
                    Name = stake.Name,

                    BigBlind = stake.BigBlind,
                    SmallBlind = stake.SmallBlind,

                    RoomID = stake.RoomID,
                    Stake = stake.Stake,
                    Prize = stake.Prize,
                    Start = stake.Start,

                    Terminate = stake.Terminate ?? stake.Start,
                    CardCodes = transactions.FirstOrDefault(x => x.PlayerID == _currentUserService.User.ID && x.TypeCard == HandCardOrder.Player)?.CardCodes ?? [],
                    Transactions = transactions
                };
            }
        }

        public async Task<StakeTournamentDetailViewModel> GetTournamentDetailAsync(int tournamentID, CancellationToken cancellationToken = default)
        {
            using (_performanceCounterService.Measure(PerformanceCategory.WebServiceStake, "GetTournamentDetail"))
            {
                var tournament = await _stakeRepository.GetTournamentAsync(tournamentID, _currentUserService.User.ID, cancellationToken);
                if (tournament?.StakeID is null)
                {
                    return null;
                }

                var prizes = await _tournamentPrizeService.ListPrizesAsync(tournamentID, cancellationToken);
                return new StakeTournamentDetailViewModel
                {
                    TournamentID = tournament.TournamentID,
                    Name = tournament.Name,
                    StakeID = tournament.StakeID.Value,

                    PayType = tournament.PayType,
                    Type = tournament.Type,
                    IsQualification = tournament.IsQualification,

                    Started = tournament.Started,
                    RegistrationClosed = tournament.RegistrationClosed,
                    Finish = tournament.Finish,
                    RebuyEnd = tournament.Started + tournament.RebuyTimeLimit,

                    Stake = tournament.Stake,
                    PrizePoolMin = GetPrizePoolMin(tournament, prizes),

                    RegistrationCount = tournament.RegistrationCount,
                    RegistrationPrice = tournament.RegistrationPrice,
                    RegistrationCommission = tournament.RegistrationCommission,

                    RebuyCount = tournament.RebuyCount,
                    RebuyPrice = tournament.RebuyPrice,
                    RebuyCommission = tournament.RebuyCommission,

                    AddonCount = tournament.AddonCount,
                    AddonPrice = tournament.AddonPrice,
                    AddonCommission = tournament.AddonCommission
                };
            }
        }

        public async Task<int?> GetClosestHandAsync(int tournamentID, DateTime date, CancellationToken cancellationToken = default)
        {
            return await _stakeRepository.GetClosestHandAsync(tournamentID, _currentUserService.User.ID, date, cancellationToken);
        }

        public async Task<StakeTournamentHandViewModel> GetTournamentHandAsync(int tournamentID, int? handID, CancellationToken cancellationToken = default)
        {
            var hand = await _stakeRepository.GetHandAsync(tournamentID, _currentUserService.User.ID, handID, cancellationToken);
            if (hand is null)
            {
                return null;
            }

            var handDetail = await _handReportRepository.GetAsync(hand.HandID, cancellationToken);
            if (handDetail is null)
            {
                return null;
            }

            var transactions = GetHandTransactions(handDetail);
            var player = handDetail.Players.Values.FirstOrDefault(x => x.PlayerID == _currentUserService.User.ID);

            if (transactions.Length == 0 || player is null)
            {
                return null;
            }

            return new StakeTournamentHandViewModel
            {
                Prize = player.Prize,
                Stake = player.Stake,
                Start = transactions.Min(x => x.Created),
                Terminate = transactions.Max(x => x.Created),
                CardCodes = transactions.FirstOrDefault(x => x.PlayerID == _currentUserService.User.ID && x.TypeCard == HandCardOrder.Player)?.CardCodes ?? [],
                Transactions = transactions,
                HandIDPrevious = hand.HandPreviousID,
                HandIDNext = hand.HandNextID,
                TableID = hand.TableID,
                HandID = hand.HandID
            };
        }

        private StakeTransactionViewModel[] GetHandTransactions(HandReportDetailDto hand)
        {
            return hand.Items
                .Where(x => x.TypeStepChoice is null && x.TypePot != PotTransactionType.AddToPot)
                .Select(x => MapTransaction(x, hand.Players))
                .ToArray();
        }

        private StakeTransactionViewModel MapTransaction(HandReportDetailItemDto item, IReadOnlyDictionary<int, HandReportDetailPlayerDto> players)
        {
            var player = item.PlayerID is not null ? players.GetValueOrDefault(item.PlayerID.Value) : null;
            var showCards = (player?.Showed ?? false) || player?.PlayerID == _currentUserService.User.ID || item.TypeCard != HandCardOrder.Player;

            return new StakeTransactionViewModel
            {
                ID = item.ID,
                Created = item.Created,

                PlayerID = player?.PlayerID,
                Nickname = player?.Nickname,

                TypeCard = item.TypeCard,
                TypeHand = item.TypeHand,
                TypePot = item.TypePot,
                TypeStep = item.TypeStep,
                TypeStepAction = item.TypeStepAction,
                TypeStepRole = item.TypeStepRole,

                Amount = item.Amount,
                CardCodes = showCards ? item.CardCodes.ToArray() : [CardCode.Hidden, CardCode.Hidden]
            };
        }

        private static decimal? GetPrizePoolMin(StakeTournamentDetailDto tournament, IEnumerable<TournamentPrizeDto> prizes)
        {
            if (tournament.Type == TournamentType.SitAndGo)
            {
                return tournament.RegistrationPrice * tournament.PlayersMax;
            }
            if (prizes is null || tournament.PrizePoolMin is not null)
            {
                return tournament.PrizePoolMin;
            }
            return tournament.PayType == TournamentPayType.Free ? prizes.Sum(x => x.ServerRewardPoints) : null;
        }
    }
}