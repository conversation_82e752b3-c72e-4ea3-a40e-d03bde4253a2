using Poker.Common.Data.Interface.Models.TournamentPlayers;
using Poker.Common.Data.Interface.Models.Tournaments;
using Poker.Web.NETCore.Enums;
using Poker.Web.NETCore.Models;

namespace Poker.Web.NETCore.Services.Tournaments
{
    public interface ITournamentsMappingService
    {
        TournamentViewModel Map(TournamentDto tournament, TournamentPlayerDto player);
        TournamentButtonState GetTournamentButtonState(TournamentViewModel tournament);
        TournamentDetailViewModel MapDetail(TournamentDto tournament, TournamentPlayerDto player);
    }
}