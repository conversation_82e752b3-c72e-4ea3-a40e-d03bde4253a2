using Poker.Common.Data.Interface.Enums;
using Poker.Common.Data.Interface.Models.Tournaments;
using Poker.Common.Data.Interface.Repositories;
using Poker.Common.Infrastructure.Performance;
using Poker.Common.Infrastructure.Settings;
using Poker.Common.Web.Authentication;
using Poker.Web.NETCore.Enums;
using Poker.Web.NETCore.Models;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Poker.Web.NETCore.Services.Tournaments
{
    public class TournamentsWebService : ITournamentsWebService
    {
        private readonly ICurrentUserService _currentUserService;
        private readonly ITournamentsMappingService _tournamentsMappingService;
        private readonly ITournamentRepository _tournamentRepository;
        private readonly ITournamentPlayerRepository _tournamentPlayerRepository;
        private readonly ISettings _settings;
        private readonly IPerformanceCounterService _performanceCounterService;
        private readonly ITournamentsCacheWebService _tournamentsCacheWebService;

        public TournamentsWebService(
            ICurrentUserService currentUserService,
            ITournamentsMappingService tournamentsMappingService,
            ITournamentRepository tournamentRepository,
            ITournamentPlayerRepository tournamentPlayerRepository,
            ISettings settings,
            IPerformanceCounterService performanceCounterService,
            ITournamentsCacheWebService tournamentsCacheWebService
        )
        {
            _currentUserService = currentUserService;
            _tournamentsMappingService = tournamentsMappingService;
            _tournamentRepository = tournamentRepository;
            _tournamentPlayerRepository = tournamentPlayerRepository;
            _settings = settings;
            _performanceCounterService = performanceCounterService;
            _tournamentsCacheWebService = tournamentsCacheWebService;
        }

        public async Task<PagedListViewModel<TournamentViewModel>> ListAsync(TournamentViewFilter filter, CancellationToken cancellationToken = default)
        {
            using (_performanceCounterService.Measure(PerformanceCategory.WebServiceTournament, "List"))
            {
                var filterRepository = new TournamentFilter
                {
                    Type = filter.Type,
                    TypeExclude = filter.TypeExclude,
                    PlayersCountFrom = filter.PlayersCountFrom,
                    PlayersCountTo = filter.PlayersCountTo,
                    StakeFrom = filter.StakeFrom,
                    StakeTo = filter.StakeTo,
                    Page = filter.Page,
                    PageSize = filter.PageSize ?? _settings.Web.PageSize,
                    Satellite = filter.Satellite is false ? false : null,
                    Voucher = filter.Qualification is false ? false : null
                };

                if (filter.State == TournamentStateViewType.Expired)
                {
                    filterRepository.State = TournamentStateType.RegistrationClosed | TournamentStateType.Started | TournamentStateType.Finished | TournamentStateType.SendCancelEmail | TournamentStateType.Stopped | TournamentStateType.FinishedByAdmin;
                    filterRepository.StartedFrom = DateTime.Now.AddDays(-7);
                    filterRepository.IncludeExpiredForPlayerID = _currentUserService.User.ID;
                }
                else if (filter.State == TournamentStateViewType.Incoming)
                {
                    filterRepository.State = TournamentStateType.Opened | TournamentStateType.RegistrationOpened;
                }
                else
                {
                    if (filter.Type == TournamentType.SitAndGo)
                    {
                        filterRepository.State = TournamentStateType.Opened | TournamentStateType.RegistrationOpened | TournamentStateType.RegistrationClosed | TournamentStateType.WaitingStarted | TournamentStateType.Started | TournamentStateType.Stopped;
                    }
                    else
                    {
                        filterRepository.State = TournamentStateType.Opened | TournamentStateType.RegistrationOpened | TournamentStateType.RegistrationClosed | TournamentStateType.WaitingStarted | TournamentStateType.Started | TournamentStateType.Stopped;
                        filterRepository.StartedFromOrNull = DateTime.Now.AddHours(-12);
                        filterRepository.StartedToOrNull = DateTime.Now.AddHours(48);
                    }
                    filterRepository.IncludeActiveForPlayerID = _currentUserService.User.ID;
                }

                if (filter.OrderByColumn != null)
                {
                    filterRepository.OrderByColumn = filter.OrderByColumn;
                    filterRepository.OrderByDirection = filter.OrderByDirection;
                }

                var tournaments = await _tournamentRepository.ListAsync(filterRepository, cancellationToken, x => x.Settings, x => x.Stats);
                var tournamentPlayers = _currentUserService.User.IsAuthenticated && filter.State == TournamentStateViewType.Current ? await _tournamentPlayerRepository.ListAsync(new()
                {
                    PlayerID = _currentUserService.User.ID,
                    Registered = true,
                    TournamentState = TournamentStateType.RegistrationOpened | TournamentStateType.RegistrationClosed | TournamentStateType.WaitingStarted | TournamentStateType.Started | TournamentStateType.Stopped,
                    TournamentStartedFromOrNull = filterRepository.StartedFromOrNull,
                    TournamentStartedToOrNull = filterRepository.StartedToOrNull
                }, cancellationToken) : null;

                var result = tournaments
                    .Select(x => _tournamentsMappingService.Map(x, tournamentPlayers?.FirstOrDefault(y => y.TournamentID == x.ID)))
                    .ToArray();

                return new PagedListViewModel<TournamentViewModel>(result, tournaments);
            }
        }

        public async Task<TournamentDetailViewModel> GetDetailAsync(int id, CancellationToken cancellationToken = default)
        {
            using (_performanceCounterService.Measure(PerformanceCategory.WebServiceTournament, "GetDetail"))
            {
                var tournament = await _tournamentRepository.GetAsync(new() { ID = id }, cancellationToken, x => x.Settings, x => x.Stats, x => x.Rebuy, x => x.Blinds, x => x.NextTournament);

                var player = _currentUserService.User.IsAuthenticated ? await _tournamentPlayerRepository.FirstOrDefaultAsync(new()
                {
                    TournamentID = tournament.ID,
                    PlayerID = _currentUserService.User.ID,
                    Registered = true
                }, cancellationToken) : null;

                var satellites = await _tournamentRepository.ListAsync(new TournamentFilter
                {
                    NextTournamentID = tournament.ID,
                    StateExclude = TournamentStateType.Created | TournamentStateType.Opened,
                    OrderByColumn = TournamentSort.Started
                }, cancellationToken, x => x.Settings, x => x.Stats);

                var model = _tournamentsMappingService.MapDetail(tournament, player);
                model.Players = await _tournamentsCacheWebService.GetPlayersAsync(model, cancellationToken);
                model.Prizes = await _tournamentsCacheWebService.GetPrizesAsync(model, cancellationToken);
                model.SatelliteTournaments = satellites.Select(x => _tournamentsMappingService.Map(x, null)).ToArray();
                if (model.Players.Length > 0)
                {
                    model.ChipsAverage = model.Players.Where(x => x.Credit > 0).DefaultIfEmpty().Average(x => x.Credit);
                    model.ChipsMin = model.Players.Where(x => x.Credit > 0).DefaultIfEmpty().Min(x => x.Credit);
                    model.ChipsMax = model.Players.Max(x => x.Credit);
                }
                return model;
            }
        }

        public async Task<TournamentViewModel> GetListItemAsync(int id, CancellationToken cancellationToken = default)
        {
            var tournament = await _tournamentRepository.GetAsync(new() { ID = id }, cancellationToken, x => x.Settings, x => x.Stats);

            var player = _currentUserService.User.IsAuthenticated ? await _tournamentPlayerRepository.FirstOrDefaultAsync(new()
            {
                TournamentID = id,
                PlayerID = _currentUserService.User.ID,
                Registered = true
            }, cancellationToken) : null;

            return _tournamentsMappingService.Map(tournament, player);
        }

        public async Task<StakeLimitViewModel> GetLimitsAsync(TournamentViewFilter filter, CancellationToken cancellationToken = default)
        {
            var stakeLimits = await _tournamentRepository.GetStakeLimitsAsync(new()
            {
                Type = filter.Type,
                TypeExclude = filter.TypeExclude,
                StartedFromOrNull = DateTime.Now.AddDays(-7)
            }, cancellationToken);

            return new StakeLimitViewModel
            {
                Min = stakeLimits?.Min,
                Max = stakeLimits?.Max
            };
        }

        public async Task<int?> GetMaxPlayersAsync(TournamentViewFilter filter, CancellationToken cancellationToken = default)
        {
            return await _tournamentRepository.GetMaxPlayersAsync(new()
            {
                Type = filter.Type,
                TypeExclude = filter.TypeExclude
            }, cancellationToken);
        }
    }
}