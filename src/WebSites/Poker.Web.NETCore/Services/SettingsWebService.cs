using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using Microsoft.Net.Http.Headers;
using Poker.Web.NETCore.Models;
using System;
using System.Text.Json;
using System.Threading.Tasks;

namespace Poker.Web.NETCore.Services
{
    public class SettingsWebService : ISettingsWebService
    {
        private const string _cookieName = "pokerSettings";
        private readonly ILogger<SettingsWebService> _log;
        private readonly IJSRuntime _jsRuntime;

        public SettingsWebService(ILogger<SettingsWebService> log, IJSRuntime jsRuntime)
        {
            _log = log;
            _jsRuntime = jsRuntime;
        }

        public SettingsViewModel Settings { get; private set; } = new();

        public void Initialize(SettingsViewModel settings)
        {
            Settings = settings;
        }

        public SettingsViewModel Initialize(HttpContext context)
        {
            if (context.Request.Cookies.TryGetValue(_cookieName, out var cookie) && cookie.Length > 0)
            {
                try
                {
                    var decoded = Convert.FromBase64String(cookie);
                    Settings = JsonSerializer.Deserialize<SettingsViewModel>(decoded) ?? new SettingsViewModel();
                }
                catch (Exception ex)
                {
                    _log.LogError(ex, "Initialize: CookieValue={CookieValue}", cookie);
                }
            }
            return Settings;
        }

        public async Task Update(Func<SettingsViewModel, SettingsViewModel> update)
        {
            Settings = update(Settings);

            var serialized = JsonSerializer.SerializeToUtf8Bytes(Settings);
            var encoded = Convert.ToBase64String(serialized);
            var cookie = new SetCookieHeaderValue(_cookieName, encoded)
            {
                Path = "/",
                Expires = DateTime.Today.AddDays(30)
            };

            await _jsRuntime.InvokeVoidAsync("poker.setCookie", cookie.ToString());
        }
    }
}