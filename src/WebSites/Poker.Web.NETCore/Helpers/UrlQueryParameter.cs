using Poker.Common.Infrastructure.Extensions;
using System;
using System.Collections.Generic;
using System.Globalization;

namespace Poker.Web.NETCore.Helpers
{
    public abstract class UrlQueryParameter
    {
        protected UrlQueryParameter(string key)
        {
            Key = key;
        }

        public string Key { get; }
        public abstract object Value { get; }
    }

    public class UrlQueryParameter<T> : UrlQueryParameter
    {
        public UrlQueryParameter(string key, T value = default, T defaultValue = default) : base(key)
        {
            Value = EqualityComparer<T>.Default.Equals(value, defaultValue) ? null : GetString(value);
        }

        public override object Value { get; }

        private static string GetString(object value)
        {
            return value switch
            {
                decimal valueDecimal => valueDecimal.Normalize().ToString(CultureInfo.InvariantCulture),
                DateTime valueDateTime => valueDateTime.ToString("yyyy-MM-dd"),
                IConvertible valueConvertible => valueConvertible.ToString(CultureInfo.InvariantCulture),
                _ => value?.ToString()
            };
        }
    }
}