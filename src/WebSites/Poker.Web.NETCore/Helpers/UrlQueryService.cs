using Microsoft.AspNetCore.Components;
using System.Linq;

namespace Poker.Web.NETCore.Helpers
{
    public class UrlQueryService : IUrlQueryService
    {
        private readonly NavigationManager _navigationManager;

        public UrlQueryService(NavigationManager navigationManager)
        {
            _navigationManager = navigationManager;
        }

        public void UpdateQuery(params UrlQueryParameter[] parameters)
        {
            var values = parameters.ToDictionary(x => x.Key, x => x.Value);
            var uri = _navigationManager.GetUriWithQueryParameters(values);
            if (uri != _navigationManager.Uri)
            {
                _navigationManager.NavigateTo(uri);
            }
        }
    }
}