using Casino.Common.NETStandard;
using Microsoft.AspNetCore.Components;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Poker.Web.NETCore.Components
{
    public partial class GameListHeaderColumn<T> : ComponentBase where T : struct, Enum
    {
        [Parameter] public Filter<T> Filter { get; set; }
        [Parameter] public T? Column { get; set; }
        [Parameter] public EventCallback OnSort { get; set; }

        [Parameter] public string Text { get; set; }
        [Parameter] public string Icon { get; set; }
        [Parameter] public string DataTestRole { get; set; }

        private bool Selected => EqualityComparer<T?>.Default.Equals(Column, Filter.OrderByColumn);

        private Task OnClickAsync()
        {
            if (Column is null)
            {
                return Task.CompletedTask;
            }

            if (Selected)
            {
                Filter.OrderByDirection = Filter.OrderByDirection == OrderingDirection.Asc ? OrderingDirection.Desc : OrderingDirection.Asc;
            }
            else
            {
                Filter.OrderByDirection = OrderingDirection.Asc;
            }

            Filter.OrderByColumn = Column;

            return OnSort.InvokeAsync();
        }
    }
}