@typeparam T

<div class="list-layout @($"list-layout--{Class}")">
    <div data-test-role="game-list" class="d-flex d-none d-md-flex justify-content-between list-layout__title">
        <div class="d-flex">
            @if (ShowChip)
            {
                <div class="list-layout__chip @($"list-layout--{Class}__chip")"></div>
            }
            <h2 data-test-role="@(!string.IsNullOrEmpty(DataTestRole)) ? game-list__title : @string.Empty">@TitleList</h2>
        </div>
        <div class="align-self-end">
            <span data-test-role="@(!string.IsNullOrEmpty(DataTestRole)) ? game-list__items-count : @string.Empty">@TitleCount</span>
        </div>
    </div>
    <div class="game-filters">
        <div @onclick="ToggleFilters" class="game-filters__header">
            <div>
                <svg xmlns="http://www.w3.org/2000/svg" width="11" height="11" fill="none" stroke="currentColor" stroke-width="2" class="bi bi-chevron-up game-filters__caret @(_showFilters ? "active" : null)" viewBox="0 0 16 16">
                    <path fill-rule="evenodd" d="M7.646 4.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1-.708.708L8 5.707l-5.646 5.647a.5.5 0 0 1-.708-.708z" />
                </svg>
                @TitleFilter
            </div>
            <div class="game-filters__toggle @(_showFilters ? "active" : null)">
                <i class="stechicons icon-filter"></i>
            </div>
        </div>
        <div class="game-filters__content @(_showFilters ? "active" : null)">
            @Filters
        </div>
    </div>
    <div class="table__headline @($"table__headline--{Class}")">
        @Columns
    </div>
    <div class="@($"table--{ClassTable}")">
        @foreach (var game in Data.Items)
        {
            @Row(game)
        }
        <PaginationResponsive Metadata="Data.Metadata" />
    </div>
</div>