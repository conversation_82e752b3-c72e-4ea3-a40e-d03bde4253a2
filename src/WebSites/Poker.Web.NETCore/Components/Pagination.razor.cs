using Microsoft.AspNetCore.Components;
using Poker.Web.NETCore.Models;
using System;

namespace Poker.Web.NETCore.Components
{
    public partial class Pagination : ComponentBase
    {
        [Inject] public NavigationManager NavigationManager { get; set; }

        [Parameter] public PagedListMetadataViewModel Metadata { get; set; }
        [Parameter] public int PageLimit { get; set; } = 10;
        [Parameter] public string Class { get; set; }

        private int PageStart
        {
            get
            {
                var pageStart = Metadata.PageNumber - PageLimit;
                var diff = PageLimit + Metadata.PageNumber - Metadata.PageCount;
                if (diff >= 0)
                {
                    pageStart -= diff + 1;
                }
                return Math.Max(pageStart, 1);
            }
        }

        private int PageEnd
        {
            get
            {
                var pageEnd = Metadata.PageNumber + PageLimit;
                var diff = PageLimit - Metadata.PageNumber + 1;
                if (diff >= 0)
                {
                    pageEnd += diff + 1;
                }
                return Math.Min(pageEnd, Metadata.PageCount);
            }
        }

        private string GetLinkClass(int index)
        {
            if (index < 1 || index > Metadata.PageCount)
            {
                return "disabled";
            }
            if (index == Metadata.PageNumber)
            {
                return "active";
            }
            return null;
        }

        private string GetUrl(int index)
        {
            return NavigationManager.GetUriWithQueryParameter("Page", index == 1 ? null : index);
        }
    }
}