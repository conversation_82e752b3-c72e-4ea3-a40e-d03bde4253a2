using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Poker.Web.NETCore.Helpers;
using Poker.Web.NETCore.Models;
using System;

namespace Poker.Web.NETCore.Components
{
    public partial class GameListLayout<T> : ComponentBase
    {
        private bool _showFilters;

        [Inject] private ILocalizerService Localizer { get; set; }
        [Inject] private IJSRuntime JSRuntime { get; set; }

        [Parameter] public PagedListViewModel<T> Data { get; set; }
        [Parameter] public string Class { get; set; }
        [Parameter] public string ClassTable { get; set; } //TODO: proč to má kurva pokaždý jinačí n<PERSON>z<PERSON>? jednou s pomlčkama a jednou bez... achjo
        [Parameter] public bool ShowChip { get; set; }

        [Parameter] public RenderFragment Filters { get; set; }
        [Parameter] public RenderFragment Columns { get; set; }
        [Parameter] public RenderFragment<T> Row { get; set; }
        [Parameter] public Func<T, object> <PERSON><PERSON><PERSON> { get; set; }

        [Parameter] public string TitleFilter { get; set; }
        [Parameter] public string TitleList { get; set; }
        [Parameter] public string TitleCount { get; set; }
        [Parameter] public string DataTestRole { get; set; }

        private void ToggleFilters()
        {
            _showFilters = !_showFilters;
        }
    }
}