<nav class="@Class">
    <ul class="pagination justify-content-center">
        @RenderLink(Metadata.PageNumber - 1, "&laquo;")
        @RenderLink(1)
        @if (PageStart == 2)
        {
            @RenderLink(2)
        }
        else if (PageStart > 1)
        {
            @RenderLink(0, "&hellip;")
        }
        @for (var i = PageStart + 1; i <= PageEnd - 1; i++)
        {
            @RenderLink(i)
        }
        @if (PageEnd == Metadata.PageCount - 1)
        {
            @RenderLink(Metadata.PageCount - 1)
        }
        else if (PageEnd < Metadata.PageCount)
        {
            @RenderLink(0, "&hellip;")
        }
        @RenderLink(Metadata.PageCount)
        @RenderLink(Metadata.PageNumber + 1, "&raquo;")
    </ul>
</nav>

@code
{
    private RenderFragment RenderLink(int index, string text = null)
    {
        return @<li class="page-item">
                   <a class="page-link @(GetLinkClass(index))" href="@GetUrl(index)">
                       @(new MarkupString(text ?? index.ToString()))
                   </a>
               </li>;
    }
}