@using Poker.Common.Data.Interface.Models.Players
@inherits PageComponentBase
@page "/PlayersReport"

<EdfGridPage AllowResize="true" SharedLocalizer="Localizer">
    <EdfGrid T="PlayerReportViewModel" TF="PlayerReportFilterViewModel" Model="_data"
             OnValidSearch="LoadPageAsync"
             SubmitOnEnter="true"
             ShowFilterInfo="true"
             UseFilterInQuery="true"
             RowIdProperty="@nameof(PlayerReportViewModel.ID)">
        <ActionsTemplate>
            <DownloadExportButton Export="ExportAsync" />
        </ActionsTemplate>

        <FilterTemplate Context="filter">
            <EdfGridFilterGroup>
                <DateTimeRangeFilterField @bind-Value="filter.Date" XlFlexSize="8" LgFlexSize="8" MdFlexSize="12" SmFlexSize="24" TimePicker="true" />
                <FormFieldEnum @bind-Value="@filter.ViewType" XlFlexSize="8" LgFlexSize="8" MdFlexSize="12" SmFlexSize="24" />
            </EdfGridFilterGroup>
        </FilterTemplate>

        <TableHeaderTemplate Context="item">
            <EdfTh Sortable="SortableOptions.AscendingFirst" SortExpression="@nameof(PlayerStatsSort.Created)">@Localizer[nameof(SharedResource.Created)]</EdfTh>
            <EdfTh Sortable="SortableOptions.AscendingFirst" SortExpression="@nameof(PlayerStatsSort.Total)" TextRight="true">@Localizer[nameof(SharedResource.PlayersCountTotal)]</EdfTh>
            <EdfTh Sortable="SortableOptions.AscendingFirst" SortExpression="@nameof(PlayerStatsSort.SitOut)" TextRight="true">@Localizer[nameof(SharedResource.PlayersCountSitOut)]</EdfTh>
            <EdfTh Sortable="SortableOptions.AscendingFirst" SortExpression="@nameof(PlayerStatsSort.Disconnected)" TextRight="true">@Localizer[nameof(SharedResource.PlayersCountDisconnected)]</EdfTh>
            <EdfTh Sortable="SortableOptions.AscendingFirst" SortExpression="@nameof(PlayerStatsSort.Unique)" TextRight="true">@Localizer[nameof(SharedResource.PlayersCountUnique)]</EdfTh>
        </TableHeaderTemplate>

        <TableRowTemplate Context="item">
            <EdfTd>@item.Created.ToDefaultString()</EdfTd>

            <EdfTd TextRight="true">@item.Total</EdfTd>
            <EdfTd TextRight="true">@item.SitOut</EdfTd>
            <EdfTd TextRight="true">@item.Disconnected</EdfTd>
            <EdfTd TextRight="true">@item.Unique</EdfTd>
        </TableRowTemplate>
    </EdfGrid>

    @if (_items is not null && IsChart)
    {
        <EdfLoading IsLoading="@_isProcessing" Class="h-75">
            <EdfChart @ref="_chart" Config="_chartConfig" />
        </EdfLoading>
    }
</EdfGridPage>