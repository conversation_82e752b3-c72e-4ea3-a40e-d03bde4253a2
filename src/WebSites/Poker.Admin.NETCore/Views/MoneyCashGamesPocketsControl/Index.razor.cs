using Casino.Common.Server.Extensions;
using EDF.Admin.Components.Models;
using EDF.Blazor.Components.Models;
using Microsoft.AspNetCore.Components;
using Poker.Admin.NETCore.Helpers;
using Poker.Admin.NETCore.Infrastructure;
using Poker.Admin.NETCore.Models.FilterView;
using Poker.Admin.NETCore.Models.ListView;
using Poker.Admin.NETCore.Resources;
using Poker.Common.Data.Interface.Models.Pockets;
using Poker.Common.Data.Interface.Repositories;
using System;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace Poker.Admin.NETCore.Views.MoneyCashGamesPocketsControl
{
    public partial class Index : PageComponentBase
    {
        private PocketsReportControlListViewModel _data;
        private PocketsReportControlDto[] _items = [];
        private PocketsReportControlDto _sum = new();

        [Inject] private IPocketReportRepository PocketReportRepository { get; set; }

        protected override PageLink PageLink => PageLinks.MoneyCashGamesPocketsControl;
        protected override PageType PageType => PageType.Index;

        [Display(Name = nameof(SharedResource.Date))]
        private DateTimeRange DateRange
        {
            get => SetDateRange(_data.Filter.From, _data.Filter.To);
            set => SetDateRange(value?.From?.LocalDateTime, value?.To?.LocalDateTime);
        }

        protected override void OnInitializedCore()
        {
            _data = new PocketsReportControlListViewModel
            {
                Filter = new PocketsReportControlFilterViewModel
                {
                    OrderByColumn = nameof(PocketsReportControlSort.PlayerID),
                    OrderByDirection = BaseFilterOrderDirection.Asc
                }
            };
            SetDateRange(null, null);
        }

        private async Task LoadPageAsync()
        {
            var filter = Mapper.Map<PocketsReportControlFilter>(_data.Filter);
            _items = await PocketReportRepository.ListControlAsync(filter);
            _sum = new PocketsReportControlDto
            {
                CreditStart = _items.Sum(x => x.CreditStart),
                CreditEnd = _items.Sum(x => x.CreditEnd),
                CreditDiff = _items.Sum(x => x.CreditDiff),
                Transactions = _items.Sum(x => x.Transactions),
                TransactionsCreditDiff = _items.Sum(x => x.TransactionsCreditDiff),
                BlockingsIn = _items.Sum(x => x.BlockingsIn),
                BlockingsOut = _items.Sum(x => x.BlockingsOut),
                Blockings = _items.Sum(x => x.Blockings),
                TableTransfers = _items.Sum(x => x.TableTransfers),
                HandsNet = _items.Sum(x => x.HandsNet),
                BlockingsHandsNet = _items.Sum(x => x.BlockingsHandsNet),
                BlockingsHandsNetDiff = _items.Sum(x => x.BlockingsHandsNetDiff)
            };
            _data.Items = _items.AsQueryable().GetPaged(filter, x => x);
        }

        private Task<IExportBuilder> ExportAsync()
        {
            var builder = ExportBuilder.Create(_items, "Report_MoneyCashGamesControl")
                .AddColumn(Localizer[nameof(SharedResource.PlayerID)], x => x.PlayerID, Localizer[nameof(SharedResource.Sum)])
                .AddColumn(nameof(PocketsReportControlDto.CreditStart), x => x.CreditStart, _sum.CreditStart)
                .AddColumn(nameof(PocketsReportControlDto.CreditEnd), x => x.CreditEnd, _sum.CreditEnd)
                .AddColumn(nameof(PocketsReportControlDto.CreditDiff), x => x.CreditDiff, _sum.CreditDiff)
                .AddColumn(nameof(PocketsReportControlDto.Transactions), x => x.Transactions, _sum.Transactions)
                .AddColumn(nameof(PocketsReportControlDto.TransactionsCreditDiff), x => x.TransactionsCreditDiff, _sum.TransactionsCreditDiff)
                .AddColumn(nameof(PocketsReportControlDto.BlockingsIn), x => x.BlockingsIn, _sum.BlockingsIn)
                .AddColumn(nameof(PocketsReportControlDto.BlockingsOut), x => x.BlockingsOut, _sum.BlockingsOut)
                .AddColumn(nameof(PocketsReportControlDto.Blockings), x => x.Blockings, _sum.Blockings)
                .AddColumn(nameof(PocketsReportControlDto.TableTransfers), x => x.TableTransfers, _sum.TableTransfers)
                .AddColumn(nameof(PocketsReportControlDto.HandsNet), x => x.HandsNet, _sum.HandsNet)
                .AddColumn(nameof(PocketsReportControlDto.BlockingsHandsNet), x => x.BlockingsHandsNet, _sum.BlockingsHandsNet)
                .AddColumn(nameof(PocketsReportControlDto.BlockingsHandsNetDiff), x => x.BlockingsHandsNetDiff, _sum.BlockingsHandsNetDiff);
            return Task.FromResult<IExportBuilder>(builder);
        }

        private DateTimeRange SetDateRange(DateTime? from, DateTime? to)
        {
            _data.Filter.From = from;
            _data.Filter.To = to;
            if (_data.Filter.From is null || _data.Filter.To is null)
            {
                _data.Filter.From = DateTime.Today.AddDays(-7);
                _data.Filter.To = DateTime.Today.AddDays(1).AddMilliseconds(-1);
            }
            return new DateTimeRange
            {
                From = _data.Filter.From,
                To = _data.Filter.To
            };
        }
    }
}