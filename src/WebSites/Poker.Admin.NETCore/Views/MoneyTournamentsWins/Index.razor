@using Poker.Common.Data.Interface.Models.TournamentPlayers
@inherits PageComponentBase
@page "/MoneyTournamentsWins"

<EdfGridPage AllowResize="true" SharedLocalizer="Localizer">
    <EdfGrid T="TournamentPlayerWinDto" TF="MoneyTournamentsWinsFilterViewModel" Model="@_data"
             OnValidSearch="LoadPageAsync"
             SubmitOnEnter="true"
             ShowFilterInfo="true"
             UseFilterInQuery="true"
             RowIdProperty="@nameof(TournamentPlayerWinDto.TournamentPlayerStakeID)">

        <FilterTemplate Context="filter">
            <EdfGridFilterGroup>
                <DateTimeRangeFilterField @bind-Value="filter.Date" XlFlexSize="8" LgFlexSize="8" MdFlexSize="12" SmFlexSize="24" TimePicker="true" />
                <FormFieldEnum @bind-Value="@filter.States" XlFlexSize="8" LgFlexSize="8" MdFlexSize="12" SmFlexSize="24" AllowClear="true" Multiple="true" />
                <FormField @bind-Value="@filter.TournamentID" XlFlexSize="8" LgFlexSize="8" MdFlexSize="12" SmFlexSize="24" />
                <FormField @bind-Value="@filter.PlayerID" XlFlexSize="8" LgFlexSize="8" MdFlexSize="12" SmFlexSize="24" />
            </EdfGridFilterGroup>
        </FilterTemplate>

        <TableHeaderTemplate Context="item">
            <EdfTh Sortable="SortableOptions.DescendingFirst" SortExpression="@nameof(TournamentPlayerWinSort.ID)">@Localizer[nameof(SharedResource.StakeID)]</EdfTh>
            <EdfTh>@Localizer[nameof(SharedResource.TournamentID)]</EdfTh>
            <EdfTh>@Localizer[nameof(SharedResource.PlayerID)]</EdfTh>
            <EdfTh>@Localizer[nameof(SharedResource.State)]</EdfTh>
            <EdfTh>@Localizer[nameof(SharedResource.Created)]</EdfTh>
            <EdfTh>@Localizer[nameof(SharedResource.Finished)]</EdfTh>
            <EdfTh>@Localizer[nameof(SharedResource.AccountType)]</EdfTh>
            <EdfTh>@Localizer[nameof(SharedResource.Amount)]</EdfTh>
            <EdfTh>@Localizer[nameof(SharedResource.Voucher)]</EdfTh>
            <EdfTh>@Localizer[nameof(SharedResource.NextTournament)]</EdfTh>
        </TableHeaderTemplate>

        <TableRowTemplate Context="item">
            <EdfTd>@item.TournamentPlayerStakeID</EdfTd>
            <EdfTd>@item.TournamentPlayerStake.TournamentID</EdfTd>
            <EdfTd>@item.TournamentPlayerStake.PlayerID</EdfTd>
            <EdfTd>
                <div class="@GetStateBadge(item)">@Localizer.GetEnum(item.State)</div>
            </EdfTd>
            <EdfTd>@(item.Created.ToDefaultLongString())</EdfTd>
            <EdfTd>@(item.Finished?.ToDefaultLongString())</EdfTd>
            <EdfTd>@Localizer.GetEnum(item.AccountType, true)</EdfTd>
            <EdfTd TextRight="true">@item.Amount.ToDefaultString()</EdfTd>
            <EdfTd>
                @if (item.VoucherTemplateID is not null)
                {
                    <a target="_blank" href="@GetVoucherUrl(item.VoucherTemplateID.Value)">@item.VoucherTemplateID</a>
                }
            </EdfTd>
            <EdfTd>
                @if (item.NextTournamentID is not null)
                {
                    <a target="_blank" href="@GetNextTournamentUrl(item.NextTournamentID.Value)">@item.NextTournamentID</a>
                }
            </EdfTd>
        </TableRowTemplate>

    </EdfGrid>
</EdfGridPage>