using Poker.Common.Data.Interface.Enums;
using Poker.Common.Data.Interface.Models.Requests;
using Poker.Common.Data.Interface.Models.TablePlayers;
using Poker.Common.Data.Interface.Repositories;
using System.Threading;
using System.Threading.Tasks;

namespace Poker.Admin.NETCore.Services
{
    public class TableService : ITableService
    {
        private readonly ITablePlayerSessionRepository _tablePlayerSessionRepository;
        private readonly IRequestRepository _requestRepository;

        public TableService(
            ITablePlayerSessionRepository tablePlayerSessionRepository,
            IRequestRepository requestRepository
        )
        {
            _tablePlayerSessionRepository = tablePlayerSessionRepository;
            _requestRepository = requestRepository;
        }

        public async Task<bool> RestartTableAsync(int id, CancellationToken cancellationToken = default)
        {
            var filter = new TablePlayerSessionFilter { TableID = id };
            var tablePlayerSession = await _tablePlayerSessionRepository.FirstOrDefaultAsync(filter, cancellationToken);

            if (tablePlayerSession is not null)
            {
                var request = new RequestDto(tablePlayerSession.Guid, RequestType.Admin, null, null, "Restarting table");
                await _requestRepository.CreateWithSequenceAsync(request, cancellationToken);
                return true;
            }

            return false;
        }
    }
}