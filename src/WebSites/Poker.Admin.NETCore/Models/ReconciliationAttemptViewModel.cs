using Casino.Common.Server.Extensions;
using Poker.Common.Data.Interface.Enums;
using Poker.Common.Data.Interface.Models.Reconciliations;
using System;

namespace Poker.Admin.NETCore.Models
{
    public class ReconciliationAttemptViewModel
    {
        public int ID { get; set; }
        public int ReconciliationRequestID { get; set; }

        public DateTime Created { get; set; }
        public DateTime? Finished { get; set; }

        public ReconciliationState State { get; set; }
        public string Result { get; set; }

        public class CustomProfile : MappingProfile
        {
            public CustomProfile()
            {
                CreateMap<ReconciliationAttemptDto, ReconciliationAttemptViewModel>();
            }
        }
    }
}