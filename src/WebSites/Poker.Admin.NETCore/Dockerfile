FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
USER $APP_UID
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["src/WebSites/Poker.Admin.NETCore/Poker.Admin.NETCore.csproj", "src/WebSites/Poker.Admin.NETCore/"]
COPY ["src/Common/Poker.Common.Data/Poker.Common.Data.csproj", "src/Common/Poker.Common.Data/"]
COPY ["src/Common/Poker.Common.Infrastructure/Poker.Common.Infrastructure.csproj", "src/Common/Poker.Common.Infrastructure/"]
COPY ["src/Common/Poker.Common.Logic.Interface/Poker.Common.Logic.Interface.csproj", "src/Common/Poker.Common.Logic.Interface/"]
COPY ["src/Common/Poker.Common.Data.Interface/Poker.Common.Data.Interface.csproj", "src/Common/Poker.Common.Data.Interface/"]
COPY ["src/Common/Poker.Common.Logic/Poker.Common.Logic.csproj", "src/Common/Poker.Common.Logic/"]
COPY ["src/Common/Poker.Common.Api.Internal/Poker.Common.Api.Internal.csproj", "src/Common/Poker.Common.Api.Internal/"]
RUN dotnet restore "src/WebSites/Poker.Admin.NETCore/Poker.Admin.NETCore.csproj"
COPY . .
WORKDIR "/src/src/WebSites/Poker.Admin.NETCore"
RUN dotnet build "Poker.Admin.NETCore.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "Poker.Admin.NETCore.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Poker.Admin.NETCore.dll"]
