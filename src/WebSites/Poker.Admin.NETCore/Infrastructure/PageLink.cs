namespace Poker.Admin.NETCore.Infrastructure
{
    public class PageLink
    {
        public PageLink(string path, string text, UserPermission[] permissionsRead, UserPermission[] permissionsEdit, params string[] breadcrumbs)
        {
            Path = path ?? string.Empty;
            Text = text;
            PermissionsRead = permissionsRead ?? [];
            PermissionsEdit = permissionsEdit ?? [];
            Breadcrumbs = breadcrumbs;
        }

        public string Path { get; }
        public string Text { get; }

        public UserPermission[] PermissionsRead { get; }
        public UserPermission[] PermissionsEdit { get; }

        public string[] Breadcrumbs { get; }
    }
}