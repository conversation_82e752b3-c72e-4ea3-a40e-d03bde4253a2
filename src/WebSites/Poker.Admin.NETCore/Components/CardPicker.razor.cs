using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.Localization;
using Microsoft.JSInterop;
using Poker.Admin.NETCore.Resources;
using Poker.Common.Data.Interface.Enums;
using Poker.Common.Data.Interface.Models.Players;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace Poker.Admin.NETCore.Components
{
    public partial class CardPicker : ComponentBase
    {
        private HashSet<(CardCodeRank first, CardCodeRank second)> _selected = [];

        [Inject] private IStringLocalizer<SharedResource> Localizer { get; set; }
        [Inject] private IJSRuntime JSRuntime { get; set; }
        [Parameter] public CardPickerViewModel Cards { get; set; }
        [Parameter] public EventCallback<CardPickerViewModel> CardsChanged { get; set; }
        [Parameter] public Expression<Func<CardPickerViewModel>> CardsExpression { get; set; }
        [CascadingParameter] public EditContext EditContext { get; set; }

        protected override void OnParametersSet()
        {
            Cards.Text = Localizer[nameof(SharedResource.CardCombinations), Cards.Combinations.Count];
        }

        private static char GetCardCodeRank(CardCodeRank rank)
        {
            return rank switch
            {
                CardCodeRank.Two => '2',
                CardCodeRank.Three => '3',
                CardCodeRank.Four => '4',
                CardCodeRank.Five => '5',
                CardCodeRank.Six => '6',
                CardCodeRank.Seven => '7',
                CardCodeRank.Eight => '8',
                CardCodeRank.Nine => '9',
                CardCodeRank.Ten => 'T',
                CardCodeRank.Jack => 'J',
                CardCodeRank.Queen => 'Q',
                CardCodeRank.King => 'K',
                CardCodeRank.Ace => 'A',
                _ => throw new ArgumentOutOfRangeException(nameof(rank), rank, null)
            };
        }

        private async Task ConfirmAsync()
        {
            EditContext.NotifyFieldChanged(FieldIdentifier.Create(CardsExpression));
            await CardsChanged.InvokeAsync(Cards);
            await JSRuntime.InvokeVoidAsync("document.activeElement.blur");
        }

        private void SelectCards(CardCodeRank first, CardCodeRank second)
        {
            var combinations = GetCombinations(first, second);
            if (_selected.Remove((first, second)))
            {
                Cards.Combinations.ExceptWith(combinations);
            }
            else
            {
                _selected.Add((first, second));
                Cards.Combinations.UnionWith(combinations);
            }
        }

        private static IEnumerable<PlayerCooperationCombinationDto> GetCombinations(CardCodeRank rankFirst, CardCodeRank rankSecond)
        {
            if (rankFirst > rankSecond)
            {
                for (var suit = CardCodeSuit.Spades; suit <= CardCodeSuit.Hearts; suit++)
                {
                    var cardFirst = CardCodeExtensions.GetCardCode(rankFirst, suit);
                    var cardSecond = CardCodeExtensions.GetCardCode(rankSecond, suit);
                    yield return GetCombination(cardFirst, cardSecond);
                }
            }
            else
            {
                for (var suitFirst = CardCodeSuit.Spades; suitFirst <= CardCodeSuit.Hearts; suitFirst++)
                {
                    for (var suitSecond = CardCodeSuit.Spades; suitSecond <= CardCodeSuit.Hearts; suitSecond++)
                    {
                        if (suitFirst != suitSecond)
                        {
                            var cardFirst = CardCodeExtensions.GetCardCode(rankFirst, suitFirst);
                            var cardSecond = CardCodeExtensions.GetCardCode(rankSecond, suitSecond);
                            yield return GetCombination(cardFirst, cardSecond);
                        }
                    }
                }
            }
        }

        private static PlayerCooperationCombinationDto GetCombination(CardCode first, CardCode second)
        {
            return first > second
                ? new PlayerCooperationCombinationDto(first, second)
                : new PlayerCooperationCombinationDto(second, first);
        }
    }
}