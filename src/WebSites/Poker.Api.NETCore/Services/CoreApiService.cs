using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Orleans;
using Poker.Api.NETCore.Hubs;
using Poker.Common.Api.Public.Core;
using Poker.Common.Api.Public.Hub;
using Poker.Common.Data.Interface.Enums;
using Poker.Common.Data.Interface.Models.Requests;
using Poker.Common.Data.Interface.Models.Sessions;
using Poker.Common.Data.Interface.Repositories;
using Poker.Common.Infrastructure.Extensions;
using Poker.Common.Infrastructure.Settings;
using Poker.Common.Logic.Interface.Messaging;
using Poker.Common.Web.Authentication;
using Poker.Engine.Interface;
using Poker.Engine.Interface.Platform;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Poker.Api.NETCore.Services
{
    public class CoreApiService : ICoreApiService
    {
        private readonly ILogger<CoreApiService> _log;
        private readonly IPlatformService _platformService;
        private readonly IRequestPublisher _requestPublisher;
        private readonly IRequestRepository _requestRepository;
        private readonly IHandReportRepository _handReportRepository;
        private readonly ITransactionMessageConsumer _transactionMessageConsumer;
        private readonly ISettings _settings;
        private readonly IServiceProvider _serviceProvider;

        public CoreApiService(
            ILogger<CoreApiService> log,
            IPlatformService platformService,
            IRequestPublisher requestPublisher,
            IRequestRepository requestRepository,
            IHandReportRepository handReportRepository,
            ITransactionMessageConsumer transactionMessageConsumer,
            ISettings settings,
            IServiceProvider serviceProvider
        )
        {
            _log = log;
            _platformService = platformService;
            _requestPublisher = requestPublisher;
            _requestRepository = requestRepository;
            _handReportRepository = handReportRepository;
            _transactionMessageConsumer = transactionMessageConsumer;
            _serviceProvider = serviceProvider;
            _settings = settings;
        }

        public async Task<CurrentPlayerApiResponse> CurrentPlayerAsync(UserIdentityDto user)
        {
            var accounts = user.IsAuthenticated ? await _platformService.PlayerAccountsAsync(new() { PlayerID = user.ID }) : null;
            return new CurrentPlayerApiResponse
            {
                PlayerID = user.IsAuthenticated ? user.ID : null,
                Nickname = user.IsAuthenticated ? user.Nickname : null,
                ExternalID = user.IsAuthenticated ? user.ExternalID : null,
                AccountFinancial = accounts?.Financial,
                AccountPoints = accounts?.Points,
                CanPlayCashGames = user.CanPlayCashGames,
                CanPlayTournaments = user.CanPlayTournaments
            };
        }

        public async Task<CreateRequestApiResponse> RequestAsync(UserIdentityDto user, SessionInfoDto session, CreateRequestApiRequest request, CancellationToken cancellationToken)
        {
            _log.LogInformation("Request start: UserID={UserID}, {@ObjRequest}", user.ID, request);

            request.Amount = request.Amount?.Normalize();
            if (request.Amount < 0)
            {
                request.Amount = 0;
            }

            if (session?.SessionGuid is null || (!session.IsVirtual && session.PlayerID != user.ID))
            {
                _log.LogWarning("Request invalid player: UserID={UserID}, {@ObjRequest}", user.ID, request);
                return new CreateRequestApiResponse { Success = false, Forbidden = true };
            }

            if (session.Table.RoomType is RoomType.CashGame or RoomType.FastCashGame && !user.CanPlayCashGames && !session.IsVirtual)
            {
                _log.LogWarning("Request invalid rights: {@ObjRequest}", request);
                return new CreateRequestApiResponse { Success = false, Forbidden = true };
            }

            if (session.Table.TableID == 0)
            {
                _log.LogWarning("Request invalid session: {@ObjRequest}", request);
                return new CreateRequestApiResponse { Success = false, Forbidden = true };
            }

            if (request.Type is RequestType.Admin || request.SubType is RequestSubType.Joined)
            {
                _log.LogWarning("Request invalid type: {@ObjRequest}", request);
                return new CreateRequestApiResponse { Success = false };
            }

            if (session.Table.RoomType is RoomType.Tournament or RoomType.SitAndGo && request.SubType is RequestSubType.Leave or RequestSubType.TournamentSitIn)
            {
                _log.LogWarning("Request invalid subtype for tournament: {@ObjRequest}", request);
                return new CreateRequestApiResponse { Success = false };
            }

            try
            {
                if (_settings.PokerServices.EngineSiloEnabled)
                {
                    var requestID = await _requestRepository.CreateAsync(new RequestDto
                    {
                        TableID = session.Table.TableID,
                        PlayerID = session.PlayerID.Value,
                        SessionGuid = session.SessionGuid.Value,
                        Type = request.Type,
                        TypeSub = request.SubType,
                        Message = !string.IsNullOrEmpty(request.Message) ? request.Message : request.TransactionID?.ToString(),
                        Place = request.Place <= 0 ? null : request.Place,
                        Amount = request.Amount <= 0 ? null : request.Amount
                    });

                    var roomKey = new RoomKey(session.Table.RoomType, session.Table.RoomID);
                    var playerKey = new PlayerKey(session.PlayerID.Value);
                    var tableKey = new TableKey(session.Table.TableID);
                    var tablePlayerKey = new TablePlayerKey(tableKey, playerKey);

                    var client = _serviceProvider.GetRequiredService<IClusterClient>();
                    var roomGrain = client.GetRoomGrain(roomKey);
                    var tableGrain = client.GetTableGrain(tableKey);

                    var amount = request.Amount <= 0 ? null : request.Amount;
                    var success = request.SubType switch
                    {
                        RequestSubType.SitIn => await roomGrain.RequestSitInAsync(tablePlayerKey, request.Place),
                        RequestSubType.SitOut => await roomGrain.RequestSitOutAsync(tablePlayerKey),
                        RequestSubType.Leave => await roomGrain.RequestLeaveAsync(tablePlayerKey),
                        RequestSubType.Message => await tableGrain.RequestMessageAsync(playerKey, request.Message),
                        RequestSubType.SmallBlindPaid => await tableGrain.RequestAction(playerKey, StepTransactionAction.SmallBlindPaid, amount),
                        RequestSubType.BigBlindPaid => await tableGrain.RequestAction(playerKey, StepTransactionAction.BigBlindPaid, amount),
                        RequestSubType.Fold => await tableGrain.RequestAction(playerKey, StepTransactionAction.Fold, amount),
                        RequestSubType.Check => await tableGrain.RequestAction(playerKey, StepTransactionAction.Check, amount),
                        RequestSubType.Raise => await tableGrain.RequestAction(playerKey, StepTransactionAction.Raise, amount),
                        RequestSubType.Bet => await tableGrain.RequestAction(playerKey, StepTransactionAction.Bet, amount),
                        RequestSubType.Call => await tableGrain.RequestAction(playerKey, StepTransactionAction.Call, amount),
                        RequestSubType.AllIn => await tableGrain.RequestAction(playerKey, StepTransactionAction.AllIn, amount),
                        RequestSubType.Show => await tableGrain.RequestAction(playerKey, StepTransactionAction.Show, amount),
                        RequestSubType.DontShow => await tableGrain.RequestAction(playerKey, StepTransactionAction.DontShow, amount),
                        RequestSubType.Muck => await tableGrain.RequestAction(playerKey, StepTransactionAction.Muck, amount),
                        RequestSubType.PenaltyPaid => await tableGrain.RequestAction(playerKey, StepTransactionAction.PenaltyPaid, amount),
                        RequestSubType.Wait => await tableGrain.RequestAction(playerKey, StepTransactionAction.Wait, amount),
                        RequestSubType.FoldQuick => await tableGrain.RequestAction(playerKey, StepTransactionAction.FoldQuick, amount),
                        _ => false
                    };

                    if (success)
                    {
                        await _requestRepository.FinishAsync(requestID);
                    }

                    _log.LogInformation("Request end: UserID={UserID}, RequestID={RequestID}", user.ID, requestID);
                    return new CreateRequestApiResponse { Success = success };
                }

                var message = new RequestDto(session.SessionGuid.Value, request.Type, request.SubType, request.Place)
                {
                    Amount = request.Amount == 0 ? null : request.Amount,
                    Message = !string.IsNullOrEmpty(request.Message) ? request.Message : request.TransactionID?.ToString()
                };
                await _requestPublisher.PublishAsync(message);
                _log.LogInformation("Request end: UserID={UserID}, RequestID={RequestID}", user.ID, message.ID);
                return new CreateRequestApiResponse { Success = true };
            }
            catch (Exception ex)
            {
                _log.LogError(ex, "Request publish failed: UserID={UserID}, {@ObjRequest}", user.ID, request);
                return new CreateRequestApiResponse { Success = false };
            }
        }

        public async Task<TableHistoryApiResponse> TableHistoryAsync(UserIdentityDto user, int tableID, CancellationToken cancellationToken)
        {
            var handIDs = await _handReportRepository.GetLastTableHandsAsync(tableID, 5, cancellationToken);
            return await HandHistoryAsync(user, handIDs, cancellationToken);
        }

        public async Task<TableHistoryApiResponse> HandHistoryAsync(UserIdentityDto user, int sessionID, CancellationToken cancellationToken)
        {
            var handIDs = await _handReportRepository.GetLastAsync(sessionID, 5, cancellationToken);
            return await HandHistoryAsync(user, handIDs, cancellationToken);
        }

        private async Task<TableHistoryApiResponse> HandHistoryAsync(UserIdentityDto user, int[] handIDs, CancellationToken cancellationToken)
        {
            var response = new TableHistoryApiResponse();

            foreach (var handID in handIDs)
            {
                var hand = await _handReportRepository.GetAsync(handID, cancellationToken);
                var player = hand.Players.FirstOrDefault(x => x.Value.PlayerID == user.ID);
                var playerInShowdown = player.Value?.Result is PlayerResult.Muck or PlayerResult.Win;

                var handHistory = new TableHistoryHand
                {
                    HandID = handID,
                    StakesSum = hand.Players.Sum(x => x.Value.Stake),

                    Cards = hand.Items
                        .Where(x => x.TypeCard is not null && x.TypeCard != HandCardOrder.Player)
                        .MaxBy(x => x.TypeCard)?.CardCodes
                        .Cast<int>()
                        .ToArray(),

                    Players = hand.Players
                        .Select(x => new TableHistoryHandPlayer
                        {
                            Place = x.Value.PlayerPlace,
                            Nickname = x.Value.Nickname,
                            Cards = hand.Items
                                .FirstOrDefault(h => h.PlayerID == x.Key && h.TypeCard == HandCardOrder.Player)?.CardCodes
                                .Select(y => (playerInShowdown && x.Value.Result is not PlayerResult.Fold) || x.Value.Showed || x.Value.PlayerID == user.ID ? y : CardCode.Hidden)
                                .Cast<int>()
                                .ToArray(),

                            Role = x.Value.Role ?? PlayerRole.None,
                            Result = x.Value.Result is PlayerResult.Muck && (playerInShowdown || x.Value.Showed || x.Value.PlayerID == user.ID)
                                ? PlayerResult.None
                                : x.Value.Result ?? PlayerResult.None,
                            Category = x.Value.Showed ? x.Value.Category : null,
                            Stake = x.Value.Stake - (x.Value.StornoStake ?? 0),
                            Prize = x.Value.Prize - (x.Value.StornoStake ?? 0),
                            CreditFinal = x.Value.CreditFinal ?? x.Value.Credit,
                            ActionLast = (StepTransactionAction?)x.Value.ActionLast
                        })
                        .ToArray()
                };
                response.Hands.Add(handHistory);
            }
            return response;
        }

        public async Task<PokerHubTransactionsResponse> Transactions(UserIdentityDto user, SessionInfoDto session, int lastTransactionSequence, CancellationToken cancellationToken)
        {
            if (session?.SessionGuid is null || (!session.IsVirtual && session.PlayerID != user.ID))
            {
                _log.LogWarning("Request invalid player: UserID={UserID}, LastTransactionSequence={LastTransactionSequence}", user.ID, lastTransactionSequence);
                return new PokerHubTransactionsResponse { Success = false };
            }

            if (session.Table.TableID == 0)
            {
                _log.LogWarning("Request invalid session: LastTransactionSequence={LastTransactionSequence}", lastTransactionSequence);
                return new PokerHubTransactionsResponse { Success = false };
            }

            var result = await _transactionMessageConsumer.ListAsync(session.Table.TableID, lastTransactionSequence, true, session.PlayerID.Value);

            _log.LogInformation("Transactions: SessionGuid={SessionGuid}, TableID={TableID}, PlayerID={PlayerID}, LastTransactionSequence={LastTransactionSequence}, Count={Count}, First={First}, Last={Last}",
                session.SessionGuid, session.Table.TableID, session.PlayerID, lastTransactionSequence, result.Count, result.FirstOrDefault(), result.LastOrDefault());

            return new PokerHubTransactionsResponse { Success = true, Transactions = result };
        }
    }
}