using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Connections;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Poker.Api.NETCore.Services;
using Poker.Common.Api.Public.Core;
using Poker.Common.Api.Public.Hub;
using Poker.Common.Infrastructure.Logging;
using Poker.Common.Infrastructure.Performance;
using Poker.Common.Logic.Interface.Services;
using Poker.Common.Web.Authentication;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Poker.Api.NETCore.Controllers
{
    [ApiController, Route("[controller]/[action]")]
    public class CoreController : ControllerBase, ICoreApiController
    {
        private readonly ILogger<CoreController> _log;
        private readonly IPerformanceCounterService _performanceCounterService;
        private readonly ISessionInfoService _sessionInfoService;
        private readonly ICurrentUserService _currentUserService;
        private readonly ICoreApiService _coreApiService;
        private readonly IPlayerSettingService _playerSettingService;

        public CoreController(
            ILogger<CoreController> log,
            IPerformanceCounterService performanceCounterService,
            ISessionInfoService sessionInfoService,
            ICurrentUserService currentUserService,
            ICoreApiService coreApiService,
            IPlayerSettingService playerSettingService
        )
        {
            _log = log;
            _performanceCounterService = performanceCounterService;
            _sessionInfoService = sessionInfoService;
            _currentUserService = currentUserService;
            _coreApiService = coreApiService;
            _playerSettingService = playerSettingService;
        }

        [HttpGet]
        public async Task<CurrentPlayerApiResponse> CurrentPlayerAsync(CancellationToken cancellationToken = default)
        {
            using (_performanceCounterService.Measure(PerformanceCategory.ApiCore, "CurrentPlayer"))
            {
                return await _coreApiService.CurrentPlayerAsync(_currentUserService.User);
            }
        }

        [HttpGet]
        public Task<bool> KeepAliveAsync(CancellationToken cancellationToken = default)
        {
            using (_performanceCounterService.Measure(PerformanceCategory.ApiCore, "KeepAlive"))
            {
                return Task.FromResult(true);
            }
        }

        [HttpGet]
        public ActionResult Test(bool exception, int code)
        {
            return exception ? throw new Exception($"Test exception: {code}") : StatusCode(code, true);
        }

        [HttpPost]
        public async Task<CreateRequestApiResponse> RequestAsync(CreateRequestApiRequest request, CancellationToken cancellationToken = default)
        {
            using (_performanceCounterService.Measure(PerformanceCategory.ApiCore, "Request"))
            {
                var session = await _sessionInfoService.GetSessionAsync(request.SessionGuid, cancellationToken);
                using (_log.UseSession(session))
                {
                    try
                    {
                        var response = await _coreApiService.RequestAsync(_currentUserService.User, session, request, cancellationToken);
                        if (response.Forbidden)
                        {
                            HttpContext.Response.StatusCode = StatusCodes.Status403Forbidden;
                        }
                        return response;
                    }
                    catch (Exception ex) when (ex is not OperationCanceledException or ConnectionResetException)
                    {
                        _log.LogError(ex, "Request error: {@ObjRequest}", request);
                        throw;
                    }
                }
            }
        }

        [HttpGet]
        public async Task<TableHistoryApiResponse> TableHistoryAsync(int tableID, CancellationToken cancellationToken = default)
        {
            using (_performanceCounterService.Measure(PerformanceCategory.ApiCore, "TableHistory"))
            {
                var table = await _sessionInfoService.GetTableAsync(tableID, cancellationToken);
                using (_log.UseTable(table))
                {
                    try
                    {
                        return await _coreApiService.TableHistoryAsync(_currentUserService.User, tableID, cancellationToken);
                    }
                    catch (Exception ex) when (ex is not OperationCanceledException or ConnectionResetException)
                    {
                        _log.LogError(ex, "TableHistory error: TableID={TableID}, PlayerID={PlayerID}", tableID, _currentUserService.User.ID);
                        throw;
                    }
                }
            }
        }

        [HttpGet]
        public async Task<TableHistoryApiResponse> HandHistoryAsync(int sessionID, CancellationToken cancellationToken = default)
        {
            using (_performanceCounterService.Measure(PerformanceCategory.ApiCore, "HandHistory"))
            {
                var session = await _sessionInfoService.GetSessionAsync(sessionID, cancellationToken);
                using (_log.UseSession(session))
                {
                    try
                    {
                        return await _coreApiService.HandHistoryAsync(_currentUserService.User, sessionID, cancellationToken);
                    }
                    catch (Exception ex) when (ex is not OperationCanceledException or ConnectionResetException)
                    {
                        _log.LogError(ex, "HandHistory error: TableID={TableID}, PlayerID={PlayerID}", sessionID, _currentUserService.User.ID);
                        throw;
                    }
                }
            }
        }

        [HttpGet]
        public async Task<PokerHubTransactionsResponse> TransactionsAsync(Guid sessionGuid, int lastTransactionSequence, CancellationToken cancellationToken = default)
        {
            using (_performanceCounterService.Measure(PerformanceCategory.ApiCore, "Transactions"))
            {
                var session = await _sessionInfoService.GetSessionAsync(sessionGuid, cancellationToken);
                using (_log.UseSession(session))
                {
                    try
                    {
                        return await _coreApiService.Transactions(_currentUserService.User, session, lastTransactionSequence, cancellationToken);
                    }
                    catch (Exception ex) when (ex is not OperationCanceledException or ConnectionResetException)
                    {
                        _log.LogError(ex, "Transactions error: SessionGuid={SessionGuid}, LastTransactionSequence={LastTransactionSequence}", sessionGuid, lastTransactionSequence);
                        throw;
                    }
                }
            }
        }

        [HttpPost]
        [Authorize]
        public async Task<bool> PlayerSettingsSaveAsync(PlayerSettingsApiDto targetPlayerSettings, CancellationToken cancellationToken = default)
        {
            using (_performanceCounterService.Measure(PerformanceCategory.ApiCore, "PlayerSettingsSave"))
            {
                try
                {
                    return await _playerSettingService.PlayerSettingsSaveAsync(_currentUserService.User.ID, targetPlayerSettings, cancellationToken);
                }
                catch (Exception ex) when (ex is not OperationCanceledException or ConnectionResetException)
                {
                    _log.LogError(ex, "PlayerSettingsSave error: TargetPlayerSettings={@ObjTargetPlayerSettings}", targetPlayerSettings);
                    throw;
                }
            }
        }

        [HttpPost]
        [Authorize]
        public async Task<PlayerSettingsApiDto[]> PlayerSettingsLoadAsync(PlayerSettingsLoadRequest playerSettingsLoadRequest, CancellationToken cancellationToken = default)
        {
            using (_performanceCounterService.Measure(PerformanceCategory.ApiCore, "PlayerSettingsLoad"))
            {
                try
                {
                    return await _playerSettingService.PlayerSettingsLoadAsync(_currentUserService.User.ID, playerSettingsLoadRequest.TargetPlayerIDs, cancellationToken);
                }
                catch (Exception ex) when (ex is not OperationCanceledException or ConnectionResetException)
                {
                    _log.LogError(ex, "PlayerSettingsLoad error: PlayerSettingsLoadRequest={@ObjPlayerSettingsLoadRequest}", playerSettingsLoadRequest);
                    throw;
                }
            }
        }
    }
}