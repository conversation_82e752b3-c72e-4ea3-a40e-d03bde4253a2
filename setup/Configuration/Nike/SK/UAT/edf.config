<?xml version="1.0"?>
<configuration>
    <appSettings>
        <add key="Culture" value="sk-SK" />
        <add key="Poker.Environment" value="UAT" />
        <add key="External.ServicesType" value="Nike" />

        <add key="Multipoker.RabbitMqFullUri" value="amqp://guest:<EMAIL>:5672" />
        <add key="Services.EngineApiUrl" value="http://EasCasEuUatAP01:5187" />
        <add key="Services.InternalApiUrl" value="http://EasCasEuUatAP01:5186" />
        <add key="Services.PlatformApiUrl" value="http://EasCasEuUatAP01:5185" />
        <add key="Services.TournamentsApiUrl" value="http://EasCasEuUatAP01:5184" />
        <add key="Web.Url" value="https://poker.stage.nike.sk" />
        <add key="Web.ClientUrl" value="https://poker-api.stage.nike.sk" />
        <add key="Web.CookieDomain" value=".stage.nike.sk" />
        <add key="Web.ContentSecurityFrameAncestorsPolicy" value="'self' https://*.stage.nike.sk" />
        <add key="Web.IframeRedirectUrl" value="https://www.stage.nike.sk/poker" />

        <add key="AdminUrl" value="https://tst-sk-nike-admin.easit.online" />
        <add key="Admin.IgnorePermissions" value="true" />

        <add key="BannedChatWordChars" value="*;_;-;.;@" />
        <add key="BannedChatWords" value="buzerant;buzerante;buzeranti;buzík;buzik;buziku;buzíci;buzici;buzna;buzno;buzny;čubka;cubka;cubko;
cubky;čubko;čubky;curak;čurák;čuráku;curaku;curaci;čuráci;čůrák;čůráku;čůráci;debil;debile;debilové;debilove;debilní;debilove;dement;
demente;dementi;děvka;devka;devko;devky;děvko;děvky;idiot;idiote;idioti;kokot;kokote;kokoti;kretén;kreten;kretene;kreteni;kreténe;kreténi;
kripl;kriple;kriplové;kriplove;krypl;kryple;kryplové;kryplove;kunda;kundo;kundy;kurva;kurvo;kurvy;piča;pica;pico;pice;pičo;piče;píča;píčo;
píče;píčus;píčusu;píčusové;picusu;picusove;poser;pošuk;posuk;pošuci;posuci;pošuku;posuku;sračka;sračko;sračky;sracka;sracko;sracky;úchyl;
uchyl;uchyle;úchyle;úchylové;uchylove;zmrd;zmrdi;zmrde;hovno;prdel;jebat;mrdat;zkurvit;kurvit;pochcat;chcat;chcát;srát;srat;šukat;šoustat;
vypič;výpič;hajzl;hajzle;hajzlové;mrdka;mrdky;mrdko;šulin;šuline;šulini;šulín;šulíni;šulíne;řiť;hovna;cigán;cigan;smažka;smažko;smažky;
smazka;smazko;smazky;feťák;feťáku;feťáci;fetak;fetaku;fetaci;zkurvenec;zkurvenci;ukurvenec;ukurvenci;chčiju;chciju;seru;sere;prcam;prcám;prcat;
zkurvysyn;zkurvysynu;zkurvysyne;jebat;jebu;ojeb;výdrb;drbat;drban;ukurvenče;ukurvence;zkurvenče;zkurvence;homouš;homous;homouši;homousi" />

        <add key="CashGames.CreateCasinoTransfers" value="false" />
        <add key="CashGames.CreateCasinoTickets" value="false" />
        <add key="CashGames.CreateBetWin" value="true" />

        <add key="Multipoker.MaxTablesOpened" value="4" />
        <add key="CashGames.AlwaysAllowMaxCredit" value="true" />
        <add key="Tournaments.AccountLoyalty" value="false" />
        <add key="Tournaments.AccountFinancialFreeroll" value="true" />

        <add key="External.OpenIDConnectAuthority" value="https://login.stage.nike.sk/api-gw/idp" />
        <add key="External.OpenIDConnectTokenEndpoint" value="https://login.stage.nike.sk/oauth2/token" />
        <add key="External.OpenIDConnectClientID" value="synottech" />
        <add key="External.OpenIDConnectClientSecret" value="synottech" />
        <add key="External.OpenIDConnectScope" value="openid poker" />
        <add key="External.OpenIDConnectClaimPlayerID" value="sub" />
        <add key="External.OpenIDConnectClaimPlayerNickname" value="nike-nickname" />
        <add key="External.ApiUrl" value="https://texas-stage.nike.sk:12440" />
        <add key="External.ApiKey" value="stagepoker" />
        <add key="External.CertificateFilePath" value="D:\nike-sk-uat\Data\Poker\Poker.Platform.NETCore\macao-stage.pfx" />
        <add key="External.CertificateFilePassword" value="PassworD!" />

        <add key="Mailing.SmtpServer" value="smtp.office365.com:587" />
        <add key="Mailing.SmtpUsername" value="<EMAIL>" />
        <add key="Mailing.SmtpPassword" value="!DUV5gwRQjPX" />
    </appSettings>

    <connectionStrings>
        <add name="Multipoker" providerName="System.Data.SqlClient" connectionString="data source=$(Database.ServerName:);Initial Catalog=$(Instance.Name:)_Casino_Multipoker;uid=etip_user;pwd=etip_user" />
    </connectionStrings>
</configuration>