using Poker.Common.Data.Interface.Enums;

namespace Poker.Tests.Unit.Tournaments
{
    public class PokerPlayer
    {
        public int TableID { get; }
        public byte Place { get; }
        public PlayerRole Role { get; }
        public int ShiftCount { get; }
        public bool IsGone { get; }

        public PokerPlayer(int tableID, byte place, PlayerRole role = PlayerRole.None, int shiftCount = 0, bool isGone = false)
        {
            TableID = tableID;
            Place = place;
            Role = role;
            ShiftCount = shiftCount;
            IsGone = isGone;
        }
    }
}