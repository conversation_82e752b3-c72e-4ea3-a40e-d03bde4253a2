using Microsoft.Extensions.DependencyInjection;
using NUnit.Framework;
using Poker.Common;
using Poker.Common.Infrastructure.Settings;
using Poker.Common.Logic.Platform;
using Poker.Common.Logic.Tournaments;
using Poker.Engine.Interface.Platform;
using Poker.Engine.Interface.Tournaments;
using Poker.Tests.Integration.Tournaments;
using Poker.Tools.Common;
using Poker.Tournaments.NETCore;
using Respawn;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading.Tasks;

namespace Poker.Tests.Integration
{
    [SetUpFixture]
    public class Setup
    {
        private static string _connectionString;
        private static Respawner _respawner;

        [OneTimeSetUp]
        public async Task SetUp()
        {
            _connectionString = Assembly.GetEntryAssembly()?.GetName().Name == "ReSharperTestRunner"
                ? "Server=localhost;Initial Catalog=eTip_MultipokerTest;uid=sa;pwd=********;TrustServerCertificate=True"
                : "Server=tst-casino.easit.cz;Initial Catalog=eTip_Multipoker;uid=etip_admin;pwd=************;TrustServerCertificate=True";

            _respawner = await Respawner.CreateAsync(_connectionString, new RespawnerOptions
            {
                WithReseed = true,
                TablesToIgnore =
                [
                    "TOR_TournamentState",
                    "TOR_TournamentType",
                    "TOR_TournamentTemplateState",
                    "PTH_Category",
                    "COR_CombItem",
                    "PTH_Setting"
                ]
            });
        }

        public static async Task<IHostWrapper> CreateHostAsync(Action<IServiceCollection> setup = null)
        {
            var host = await Tools.Common.ServiceExtensions.CreateHostAsync(new Dictionary<string, string>
            {
                ["ConnectionStrings:Multipoker"] = _connectionString,
                ["AppSettings:External.ServicesType"] = nameof(ExternalServicesType.Mock),
                ["AppSettings:Poker.Environment"] = nameof(HostingEnvironment.DEV),
                ["AppSettings:CashGames.CreateCasinoTransfers"] = bool.FalseString,
                ["AppSettings:CashGames.CreateCasinoTickets"] = bool.FalseString,
                ["AppSettings:Game.HackCards"] = bool.TrueString
            }, x =>
            {
                x.AddPokerCommon();
                x.AddTransient<TournamentTestHelper>();
                x.AddSingleton<IPlatformService, MockPlatformService>();
                x.AddSingleton<TournamentLockService>();
                x.AddTransient<ITournamentRegisterService, TournamentRegisterService>();
                x.AddTransient<ITournamentEvaluationService, TournamentEvaluationService>();
                x.AddTransient<ITournamentPayoutService, TournamentPayoutService>();
                setup?.Invoke(x);
            });

            await _respawner.ResetAsync(_connectionString);

            return host;
        }
    }
}