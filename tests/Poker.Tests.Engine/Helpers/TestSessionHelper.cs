using AwesomeAssertions;
using Microsoft.Extensions.Logging.Abstractions;
using Poker.Common.Data.Interface.Enums;
using Poker.Common.Data.Interface.Models.Transactions;
using Poker.Engine.Interface;
using Poker.Engine.Interface.Tables;
using Poker.Engine.Queue;
using Poker.Engine.Tables;
using System.Linq;
using System.Threading.Tasks;

namespace Poker.Tests.Engine.Helpers
{
    public class TestSessionHelper
    {
        private readonly int _timeout;

        public TestSessionHelper(TestTransactionsListener transactions, TestPlayerInfo player, int timeout)
        {
            _timeout = timeout;
            Player = player;
            if (Player.TableID is not null)
            {
                Table = new(transactions, player.TableID.Value);
            }
        }

        public PlayerKey PlayerKey => Player.Player;
        public TestPlayerInfo Player { get; }
        public TestTableHelper Table { get; }

        public async Task<ITableGrainStateWrapper> GetState()
        {
            var grain = Setup.GetGrain<ITableGrain>(Player.TableID.Value);
            var state = await grain.GetStateAsync();
            var wrapper = new TableGrainStateWrapper(new TestStateWrapper<TableGrainState> { State = state }, NullLogger.Instance, Setup.GetService<IQueueService>());
            return wrapper;
        }

        public async Task Leave()
        {
            var grain = Setup.GetRoomGrain(new(Player.RoomType, Player.RoomID));
            await grain.RequestLeaveAsync(new(new(Player.TableID.Value), PlayerKey));
            await Table.Transactions.Wait(new SitTransactionDto
            {
                TypeSub = SitTransactionType.SitOut,
                PlayerID = PlayerKey.ID,
                Nickname = Player.Nickname,
                Place = Player.Place
            });
        }

        public async Task SitIn(byte place, bool processed = true)
        {
            var grain = Setup.GetRoomGrain(new(Player.RoomType, Player.RoomID));
            var result = await grain.RequestSitInAsync(new(new(Player.TableID.Value), PlayerKey), place);
            if (processed)
            {
                result.Should().BeTrue();
                await Table.Transactions.Wait(new SitTransactionDto
                {
                    TypeSub = SitTransactionType.SitIn,
                    PlayerID = PlayerKey.ID,
                    Nickname = Player.Nickname,
                    Place = Player.Place ?? place
                });
                Player.Place = place;
            }
            else
            {
                result.Should().BeFalse();
            }
        }

        public async Task SitOut(bool processed = true)
        {
            var grain = Setup.GetRoomGrain(new(Player.RoomType, Player.RoomID));
            var result = await grain.RequestSitOutAsync(new(new(Player.TableID.Value), PlayerKey));
            if (processed)
            {
                result.Should().BeTrue();
                await Table.Transactions.Wait(new SitTransactionDto
                {
                    TypeSub = SitTransactionType.SitOut,
                    PlayerID = PlayerKey.ID,
                    Nickname = Player.Nickname,
                    Place = Player.Place
                });
            }
            else
            {
                result.Should().BeFalse();
            }
        }

        public async Task Action(StepTransactionAction action, decimal amount, decimal creditAbs)
        {
            var grain = Setup.GetGrain<ITableGrain>(Player.TableID.Value);
            var result = await grain.RequestAction(PlayerKey, action, amount);
            result.Should().BeTrue();
            await WaitForAction(action, amount, creditAbs);
        }

        public async Task ActionSmallBlind(decimal amount, decimal creditAbs)
        {
            await Action(StepTransactionAction.SmallBlindPaid, amount, creditAbs);
        }

        public async Task ActionBigBlind(decimal amount, decimal creditAbs)
        {
            await Action(StepTransactionAction.BigBlindPaid, amount, creditAbs);
        }

        public async Task ActionPenalty(decimal amount, decimal creditAbs)
        {
            await Action(StepTransactionAction.PenaltyPaid, amount, creditAbs);
        }

        public async Task ActionWait(decimal creditAbs)
        {
            await Action(StepTransactionAction.Wait, 0, creditAbs);
        }

        public async Task ActionCall(decimal callAmount, decimal creditAbs, decimal? betAmount = null)
        {
            var choices = StepTransactionChoice.Fold | StepTransactionChoice.Call | StepTransactionChoice.AllIn;
            if (betAmount is not null)
            {
                choices |= StepTransactionChoice.Raise;
            }
            await WaitForChoice(choices, betAmount, callAmount);
            await Action(StepTransactionAction.Call, callAmount, creditAbs);
        }

        public async Task ActionCheck(decimal creditAbs, decimal? betAmount = null, StepTransactionChoice betChoice = StepTransactionChoice.Bet)
        {
            var choices = StepTransactionChoice.Fold | StepTransactionChoice.Check | StepTransactionChoice.AllIn;
            if (betAmount is not null)
            {
                choices |= betChoice;
            }
            await WaitForChoice(choices, betAmount);
            await Action(StepTransactionAction.Check, 0, creditAbs);
        }

        public async Task WaitForAction(StepTransactionAction action, decimal amount, decimal creditAbs)
        {
            await Table.Transactions.Wait(new StepTransactionDto
            {
                TypeSub = StepTransactionType.Action,
                PlayerID = PlayerKey.ID,
                Action = action is StepTransactionAction.FoldQuick ? StepTransactionAction.Fold : action,
                LiveBetAdd = amount,
                CreditAbs = creditAbs
            });
        }

        public async Task WaitForJoin()
        {
            await Table.Transactions.Wait(new SitTransactionDto
            {
                TypeSub = SitTransactionType.Joined,
                PlayerID = PlayerKey.ID,
                Nickname = Player.Nickname
            });
        }

        public async Task WaitForMoney(decimal amount, decimal? creditAbs = null)
        {
            await Table.Transactions.Wait(new SitTransactionDto
            {
                TypeSub = SitTransactionType.MoneyEnd,
                PlayerID = PlayerKey.ID,
                Nickname = Player.Nickname,
                Amount = amount,
                CreditAbs = creditAbs ?? amount
            });
        }

        public async Task WaitForSitIn()
        {
            await Table.Transactions.Wait(new SitTransactionDto
            {
                TypeSub = SitTransactionType.SitIn,
                PlayerID = PlayerKey.ID,
                Nickname = Player.Nickname,
                Place = Player.Place
            });
        }

        public async Task WaitForSitOut()
        {
            await Table.Transactions.Wait(new SitTransactionDto
            {
                TypeSub = SitTransactionType.SitOut,
                PlayerID = PlayerKey.ID,
                Nickname = Player.Nickname,
                Place = Player.Place
            }, _timeout + 3);
        }

        public async Task WaitForLeave()
        {
            await Table.Transactions.Wait(new SitTransactionDto
            {
                TypeSub = SitTransactionType.Leave,
                PlayerID = PlayerKey.ID,
                Nickname = Player.Nickname,
                Place = Player.Place
            });
        }

        public async Task WaitForRole(PlayerRole role)
        {
            await Table.Transactions.Wait(new StepTransactionDto
            {
                TypeSub = StepTransactionType.Role,
                PlayerID = PlayerKey.ID,
                Role = role
            });
        }

        public async Task WaitForChoice(StepTransactionChoice choice, decimal? betAmount = null, decimal? callAmount = null, int? timeout = null)
        {
            await Table.Transactions.Wait(new StepTransactionDto
            {
                TypeSub = StepTransactionType.Choice,
                PlayerID = PlayerKey.ID,
                TimeOut = timeout ?? _timeout,
                Choice = choice,
                Prize = betAmount,
                CreditAbs = callAmount
            });
        }

        public async Task WaitForStorno(decimal amount, decimal creditAbs)
        {
            await Table.Transactions.Wait(new StepTransactionDto
            {
                TypeSub = StepTransactionType.Storno,
                PlayerID = PlayerKey.ID,
                LiveBetAdd = amount,
                CreditAbs = creditAbs
            });
        }

        public async Task WaitForCard(byte order)
        {
            var cards = TestPlayerProvider.GetCards(PlayerKey);
            await Table.Transactions.Wait(new HandCardTransactionDto
            {
                PlayerID = PlayerKey.ID,
                PlayerOrder = order,
                CardCode = cards[order - 1]
            });
        }

        private async Task WaitForBestCards(HandState roundID, CardsCategory cardsCategory, CardCode card1, CardCode card2, CardCode card3, CardCode card4, CardCode card5)
        {
            var cards = TestPlayerProvider.GetCards(PlayerKey);
            await Table.Transactions.Wait(new StepTransactionDto
            {
                TypeSub = StepTransactionType.PlayerBestCards,
                PlayerID = PlayerKey.ID,
                RoundID = roundID,
                Cards = cards.ToList(),
                CardsBest = [card1, card2, card3, card4, card5],
                CategoryID = cardsCategory,
                Show = roundID is HandState.Showdown
            });
        }

        public async Task WaitForBestCards(HandState roundID)
        {
            var bestCards = TestPlayerProvider.GetBestCards(PlayerKey);
            await WaitForBestCards(roundID, bestCards.Category, bestCards.Cards[0], bestCards.Cards[1], bestCards.Cards[2], bestCards.Cards[3], bestCards.Cards[4]);
        }

        public async Task<TestSessionHelper> WaitForTableChange()
        {
            await Table.Transactions.Wait(new SitTransactionDto
            {
                TypeSub = SitTransactionType.ChangeTable,
                PlayerID = PlayerKey.ID,
                Nickname = Player.Nickname
            });
            await WaitForSitOut();
            return this;
        }

        public async Task WaitForTournamentLoss(int? rank)
        {
            await Table.Transactions.Wait(new SitTransactionDto
            {
                TypeSub = SitTransactionType.TournamentLooser,
                PlayerID = PlayerKey.ID,
                Nickname = Player.Nickname,
                PlayerRank = rank
            });
        }

        public async Task WaitForTournamentWin()
        {
            await Table.Transactions.Wait(new SitTransactionDto
            {
                TypeSub = SitTransactionType.TournamentWinner,
                PlayerID = PlayerKey.ID,
                Nickname = Player.Nickname
            });
        }
    }
}