using Poker.Common.Data.Interface.Enums;
using Poker.Engine.Interface;
using System;

namespace Poker.Tests.Engine.Helpers
{
    public class TestPlayerInfo
    {
        public required int RoomID { get; init; }
        public required RoomType RoomType { get; init; }
        public required PlayerKey Player { get; init; }
        public required string Nickname { get; init; }
        public required SessionRoomKey Session { get; init; }

        public int? TableID { get; init; }
        public Guid? SessionGuid { get; init; }

        public byte? Place { get; set; }
    }
}