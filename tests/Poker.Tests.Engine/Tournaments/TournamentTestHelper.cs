using AwesomeAssertions;
using Poker.Common.Data.Interface.Enums;
using Poker.Common.Data.Interface.Models.TournamentBlinds;
using Poker.Common.Data.Interface.Models.TournamentPrizes;
using Poker.Common.Data.Interface.Models.Tournaments;
using Poker.Common.Data.Interface.Repositories;
using Poker.Common.Logic.Interface.Tournaments;
using Poker.Engine.Interface;
using Poker.Engine.Interface.Tournaments;
using Poker.Tests.Engine.Helpers;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Poker.Tests.Engine.Tournaments
{
    public class TournamentTestHelper
    {
        private readonly TestTransactionsListener _transactions;
        private readonly TournamentDto _tournament;

        private TournamentTestHelper(TestTransactionsListener transactions, TournamentDto tournament)
        {
            _transactions = transactions;
            _tournament = tournament;
        }

        public int TournamentID => _tournament.ID;

        public static async Task<TournamentTestHelper> Create(Action<TournamentDto> setup = null)
        {
            var repository = Setup.GetService<ITournamentAdminService>();
            var tournament = new TournamentDto
            {
                Name = "Tournament",
                State = TournamentStateType.Opened,
                Opened = DateTime.Now.AddHours(-1),
                Settings = new()
                {
                    Type = TournamentType.SitAndGo,
                    Started = DateTime.Now.AddHours(-1),
                    RegistrationOpened = DateTime.Now.AddHours(-1),
                    RegistrationClosed = DateTime.Now.AddHours(-1),

                    PayType = TournamentPayType.RealMoney,
                    AccountTypeRegistration = AccountType.Financial,
                    AccountTypePrize = AccountType.Financial,

                    PlayersMin = 2,
                    PlayersMax = 1000,
                    StartingChipsAmount = 5000,

                    TablePlayersMax = 9,
                    FinalTableMaxPlayers = 9,
                    TurnTimeout = 10,

                    BuyIn = 100,
                    Commission = 10
                },
                Blinds =
                [
                    new TournamentBlindDto { Start = 0, Small = 1, Big = 2, Ante = 1 }
                ],
                Prizes =
                [
                    new TournamentPrizeDto { Sequence = 1, PrizePoolFactor = 1m }
                ]
            };
            setup?.Invoke(tournament);
            tournament = await repository.CreateOrUpdateAsync(tournament);

            var transactions = Setup.GetService<TestTransactionsListener>();
            return new TournamentTestHelper(transactions, tournament);
        }

        public async Task Register(PlayerKey player, AccountType accountType = AccountType.Financial)
        {
            var repository = Setup.GetService<IPlayerRepository>();
            await repository.CreateOrUpdateAsync(player.ID, $"nick{player.ID}");

            var grain = Setup.GetGrain<ITournamentGrain>(_tournament.ID);
            var result = await grain.RegisterAsync(new TournamentRegisterRequest { PlayerID = player.ID, AccountType = accountType });
            result.Result.Should().Be(TournamentRegisterResult.Success);
        }

        public async Task RegistrationOpen()
        {
            var grain = Setup.GetGrain<ITournamentGrain>(_tournament.ID);
            var result = await grain.RegistrationOpenAsync();
            result.Should().BeTrue();
        }

        public async Task RegistrationClose()
        {
            var grain = Setup.GetGrain<ITournamentGrain>(_tournament.ID);
            var result = await grain.RegistrationCloseAsync();
            result.Should().BeTrue();
        }

        public async Task Start()
        {
            var grain = Setup.GetGrain<ITournamentGrain>(_tournament.ID);
            var result = await grain.StartAsync();
            result.Should().BeTrue();
        }

        public async Task Finish()
        {
            var grain = Setup.GetGrain<ITournamentGrain>(_tournament.ID);
            var result = await grain.FinishAsync();
            result.Should().BeTrue();
        }

        public async Task Payout()
        {
            var grain = Setup.GetGrain<ITournamentGrain>(_tournament.ID);
            var result = await grain.PayoutAsync();
            result.Should().BeTrue();
        }

        public async Task<TestSessionHelper> Join(PlayerKey playerKey)
        {
            var grain = Setup.GetGrain<ITournamentGrain>(_tournament.ID);
            var join = await grain.JoinAsync(playerKey);
            join.Result.Should().Be(ClientStateTournamentResult.Success);

            var player = new TestPlayerInfo
            {
                RoomID = _tournament.ID,
                RoomType = RoomType.Tournament,
                Player = playerKey,
                Nickname = $"nick{playerKey}",
                Session = new(join.Player.SessionID),
                TableID = join.Table?.TableID,
                SessionGuid = join.Table?.SessionGuid,
                Place = join.Table?.Players.FirstOrDefault(x => x.PlayerID == playerKey.ID)?.Place
            };

            var session = new TestSessionHelper(_transactions, player, _tournament.Settings.TurnTimeout);
            await session.WaitForJoin();
            await session.WaitForSitIn();
            await session.WaitForMoney(_tournament.Settings.StartingChipsAmount.Value);
            return session;
        }

        public async Task<TestSessionHelper> ChangeTable(TestSessionHelper session, decimal amount)
        {
            session = await Join(session.PlayerKey);
            await session.WaitForJoin();
            await session.WaitForSitIn();
            await session.WaitForMoney(amount);
            return session;
        }
    }
}