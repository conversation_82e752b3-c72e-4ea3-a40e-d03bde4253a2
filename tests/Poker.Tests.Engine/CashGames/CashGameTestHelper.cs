using AwesomeAssertions;
using Poker.Common.Data.Interface.Enums;
using Poker.Common.Data.Interface.Models.CashGames;
using Poker.Common.Data.Interface.Repositories;
using Poker.Engine.Interface;
using Poker.Engine.Interface.CashGames;
using Poker.Engine.Interface.Rooms;
using Poker.Engine.Queue;
using Poker.Engine.Rooms;
using Poker.Tests.Engine.Helpers;
using System;
using System.Threading.Tasks;

namespace Poker.Tests.Engine.CashGames
{
    public class CashGameTestHelper
    {
        private readonly TestTransactionsListener _transactions;
        private readonly int _roomID;
        private readonly int _timeout;

        private CashGameTestHelper(TestTransactionsListener transactions, int roomID, int timeout)
        {
            _transactions = transactions;
            _roomID = roomID;
            _timeout = timeout;
        }

        public static async Task<CashGameTestHelper> Create(Action<CashGameDto> setup = null)
        {
            var cashGameRepository = Setup.GetService<ICashGameRepository>();
            var cashGame = new CashGameDto
            {
                Name = "CashGame",
                RoomType = RoomType.CashGame,
                RulesType = RulesType.NoLimit,
                PlayersMin = 2,
                PlayersMax = 9,
                SmallBlind = 1,
                BigBlind = 2,
                CreditMin = 0,
                CreditMax = 999999,
                TurnTimeout = 10,
                Active = true
            };
            setup?.Invoke(cashGame);
            var room = await cashGameRepository.CreateAsync(cashGame);

            var transactions = Setup.GetService<TestTransactionsListener>();
            return new CashGameTestHelper(transactions, room.ID, cashGame.TurnTimeout);
        }

        public async Task<IRoomGrainStateWrapper> GetState()
        {
            var grain = Setup.GetGrain<ICashGameGrain>(_roomID);
            var state = await grain.GetStateAsync();
            var wrapper = new RoomGrainStateWrapper(new TestStateWrapper<RoomGrainState> { State = state }, Setup.GetService<IQueueService>());
            wrapper.Initialize();
            return wrapper;
        }

        public async Task<TestSessionHelper> Join(PlayerKey playerKey)
        {
            var nickname = $"nick{playerKey}";

            var cashGameGrain = Setup.GetGrain<ICashGameGrain>(_roomID);
            var join = await cashGameGrain.JoinAsync(playerKey, nickname);
            join.Result.Should().Be(ClientStateCashGameResult.Success);

            var player = new TestPlayerInfo
            {
                RoomID = _roomID,
                RoomType = RoomType.CashGame,
                Player = new(join.Player.PlayerID),
                Nickname = nickname,
                Session = new(join.Player.SessionID),
                TableID = join.Table.TableID,
                SessionGuid = join.Table.SessionGuid
            };

            var session = new TestSessionHelper(_transactions, player, _timeout);
            await session.WaitForJoin();
            return session;
        }

        public async Task<TestSessionHelper> JoinSitInRecharge(PlayerKey playerKey, byte place, decimal amount, bool auto = false)
        {
            var helper = await Join(playerKey);
            await helper.SitIn(place);
            await Recharge(helper, amount, auto);
            return helper;
        }

        public async Task RechargeInfo(TestSessionHelper session, decimal currentCredit)
        {
            var grain = Setup.GetGrain<ICashGameGrain>(session.Player.RoomID);
            var result = await grain.RechargeInfoAsync(session.Player.Session);

            result.Result.Should().Be(CashGameRechargeInfoResult.Success);
            result.CurrentCredit.Should().Be(currentCredit);
        }

        public async Task Recharge(TestSessionHelper session, decimal amount, bool auto, decimal? creditAbs = null)
        {
            var grain = Setup.GetGrain<ICashGameGrain>(session.Player.RoomID);
            var result = await grain.RechargeAsync(session.Player.Session, amount, auto);

            result.Result.Should().Be(CashGameRechargeResult.Success);

            if (session.Player.TableID is not null)
            {
                await session.WaitForMoney(amount, creditAbs);
            }
        }
    }
}