using AwesomeAssertions;
using Poker.Common.Data.Interface.Enums;
using Poker.Common.Data.Interface.Models.CashGames;
using Poker.Common.Data.Interface.Repositories;
using Poker.Engine.Interface;
using Poker.Engine.Interface.CashGames;
using Poker.Engine.Interface.Rooms;
using Poker.Engine.Queue;
using Poker.Engine.Rooms;
using Poker.Tests.Engine.Helpers;
using System.Linq;
using System.Threading.Tasks;

namespace Poker.Tests.Engine.FastCashGames
{
    public class FastCashGameTestHelper
    {
        private readonly TestTransactionsListener _transactions;
        private readonly int _roomID;
        private readonly int _timeout;

        private FastCashGameTestHelper(TestTransactionsListener transactions, int roomID, int timeout)
        {
            _transactions = transactions;
            _roomID = roomID;
            _timeout = timeout;
        }

        public static async Task<FastCashGameTestHelper> Create(int timeout = 15)
        {
            var repository = Setup.GetService<ICashGameRepository>();
            var room = await repository.CreateAsync(new CashGameDto
            {
                Name = "FastCashGame",
                RoomType = RoomType.FastCashGame,
                RulesType = RulesType.NoLimit,
                SmallBlind = 1,
                BigBlind = 2,
                CreditMin = 0,
                CreditMax = 999999,
                TurnTimeout = timeout,
                Active = true
            });

            var transactions = Setup.GetService<TestTransactionsListener>();
            return new FastCashGameTestHelper(transactions, room.ID, timeout);
        }

        public async Task<IRoomGrainStateWrapper> GetState()
        {
            var grain = Setup.GetGrain<IFastCashGameGrain>(_roomID);
            var state = await grain.GetStateAsync();
            var wrapper = new RoomGrainStateWrapper(new TestStateWrapper<RoomGrainState> { State = state }, Setup.GetService<IQueueService>());
            wrapper.Initialize();
            return wrapper;
        }

        public async Task<TestSessionHelper> Join(PlayerKey playerKey, ClientStateCashGameResult result, SessionRoomKey? sessionRoomKey = null)
        {
            var nickname = $"nick{playerKey}";

            var grain = Setup.GetGrain<IFastCashGameGrain>(_roomID);
            var join = await grain.JoinAsync(playerKey, nickname, sessionRoomKey);
            join.Result.Should().Be(result);

            var player = new TestPlayerInfo
            {
                RoomID = _roomID,
                RoomType = RoomType.FastCashGame,
                Player = new(join.Player.PlayerID),
                Nickname = nickname,
                Session = new(join.Player.SessionID),
                TableID = join.Table?.TableID,
                SessionGuid = join.Table?.SessionGuid,
                Place = join.Table?.Players.FirstOrDefault(x => x.PlayerID == playerKey.ID)?.Place
            };

            return new TestSessionHelper(_transactions, player, _timeout);
        }

        public async Task<TestSessionHelper> JoinAndRecharge(PlayerKey playerKey, decimal amount, int? playerCount = null, bool modeChange = false)
        {
            var session = await Join(playerKey, ClientStateCashGameResult.Recharge);
            await Recharge(session, amount, false);
            session = await Join(playerKey, ClientStateCashGameResult.Success, session.Player.Session);
            session = await Join(session.PlayerKey, ClientStateCashGameResult.Success, session.Player.Session);
            await WaitForJoin(session, amount, playerCount, modeChange);
            return session;
        }

        public async Task<TestSessionHelper> ChangeTable(TestSessionHelper session, decimal amount, bool modeChange = false)
        {
            session = await Join(session.PlayerKey, ClientStateCashGameResult.Success, session.Player.Session);
            await WaitForJoin(session, amount, null, modeChange);
            return session;
        }

        private async Task WaitForJoin(TestSessionHelper session, decimal amount, int? playerCount = null, bool modeChange = false)
        {
            if (modeChange)
            {
                await session.Table.WaitForTableChangeEnabled();
            }
            await session.WaitForJoin();
            await session.WaitForSitIn();
            await session.WaitForMoney(amount);
            if (playerCount is not null)
            {
                await session.Table.WaitForPlayerCount(playerCount.Value);
            }
        }

        public async Task RechargeInfo(TestSessionHelper session, decimal currentCredit)
        {
            var grain = Setup.GetGrain<IFastCashGameGrain>(session.Player.RoomID);
            var result = await grain.RechargeInfoAsync(session.Player.Session);
            result.Result.Should().Be(CashGameRechargeInfoResult.Success);
            result.CurrentCredit.Should().Be(currentCredit);
        }

        public async Task Recharge(TestSessionHelper session, decimal amount, bool auto, decimal? creditAbs = null)
        {
            var grain = Setup.GetGrain<IFastCashGameGrain>(session.Player.RoomID);
            var result = await grain.RechargeAsync(session.Player.Session, amount, auto);
            result.Result.Should().Be(CashGameRechargeResult.Success);

            if (session.Player.TableID is not null)
            {
                await session.WaitForMoney(amount, creditAbs);
            }
        }
    }
}