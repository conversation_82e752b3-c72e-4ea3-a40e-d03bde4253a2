using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NUnit.Framework;
using Orleans;
using Orleans.TestingHost;
using Poker.Common.Infrastructure;
using Poker.Common.Infrastructure.Settings;
using Poker.Engine;
using Poker.Engine.Interface;
using Poker.Engine.Interface.Rooms;
using Poker.Tests.Engine.Helpers;
using Respawn;
using System.Collections.Generic;
using System.Reflection;
using System.Threading.Tasks;

namespace Poker.Tests.Engine
{
    [SetUpFixture]
    public class Setup
    {
        private static InProcessTestCluster _cluster;

        [OneTimeSetUp]
        public async Task SetUp()
        {
            TestLogger.SetContext();

            var connectionString = Assembly.GetEntryAssembly()?.GetName().Name == "ReSharperTestRunner"
                ? "Server=localhost;Initial Catalog=eTip_MultipokerTest;uid=sa;pwd=********;TrustServerCertificate=True"
                : "Server=tst-casino.easit.cz;Initial Catalog=eTip_Multipoker;uid=etip_admin;pwd=************;TrustServerCertificate=True";

            var respawner = await Respawner.CreateAsync(connectionString, new RespawnerOptions
            {
                WithReseed = true,
                TablesToIgnore =
                [
                    "TOR_TournamentState",
                    "TOR_TournamentType",
                    "TOR_TournamentTemplateState",
                    "PTH_Category",
                    "COR_CombItem",
                    "PTH_Setting"
                ]
            });
            await respawner.ResetAsync(connectionString);

            var builder = new InProcessTestClusterBuilder(1);
            builder.ConfigureHost(host =>
            {
                host.Logging.ClearProviders();
                host.Logging.AddProvider(new TestLoggerProvider());
                host.Logging.AddFilter("Microsoft", LogLevel.Warning);
                host.Configuration.AddInMemoryCollection(new Dictionary<string, string>
                {
                    ["ConnectionStrings:Multipoker"] = connectionString,
                    ["AppSettings:External.ServicesType"] = nameof(ExternalServicesType.Mock),
                    ["AppSettings:Poker.Environment"] = nameof(HostingEnvironment.DEV),
                    ["AppSettings:CashGames.CreateCasinoTransfers"] = bool.FalseString,
                    ["AppSettings:CashGames.CreateCasinoTickets"] = bool.FalseString,
                    ["AppSettings:Game.HackCards"] = bool.TrueString
                });
                host.ConfigurePokerInfrastructure(false);
                Startup.ConfigureServices(host);
            });
            builder.ConfigureSilo((_, silo) =>
            {
                Startup.ConfigureSilo(silo);
            });
            builder.ConfigureClient(client =>
            {
                client.Services.AddSingleton<TestTransactionsListener>();
                client.Services.AddHostedService(y => y.GetRequiredService<TestTransactionsListener>());
            });

            _cluster = builder.Build();
            await _cluster.DeployAsync();
        }

        [OneTimeTearDown]
        public static async Task TearDown()
        {
            TestLogger.SetContext();

            if (_cluster is not null)
            {
                await _cluster.DisposeAsync();
            }
        }

        public static T GetGrain<T>(int key) where T : IGrainWithIntegerKey
        {
            return _cluster.Client.GetGrain<T>(key);
        }

        public static IRoomGrain GetRoomGrain(RoomKey roomKey)
        {
            return _cluster.Client.GetRoomGrain(roomKey);
        }

        public static T GetService<T>()
        {
            return _cluster.Client.ServiceProvider.GetRequiredService<T>();
        }
    }
}